using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using Inspur.Abp.Ledger.Core;
using Inspur.Abp.Ledger.Core.Views;
using Inspur.Abp.Ledger.EntityFrameworkCore;
using Inspur.Abp.Platform.Organizational;
using Volo.Abp.EntityFrameworkCore;

namespace Inspur.Abp.Ledger.OceanBase.Dapper.Repository.Core;

public class LedgerUserDapperRepository : LedgerUserDapperRepositoryBase
{
    public LedgerUserDapperRepository(IDbContextProvider<ILedgerDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

    /// <summary>
    /// 获取拥有审核权限的台账授权用户
    /// </summary>
    /// <returns></returns>
    public override async Task<List<LedgerUser>> GetAuditUserAsync(Guid ledgerId, Guid department)
    {
        var connection = await GetDbConnectionAsync();
        var sql = @"SELECT 
            lu.`CreationTime`,
            lu.`CreatorId`,
            lu.`LedgerId`,
            lu.`UserId`,
            lu.`DepartmentId`,
            lu.`Permissions`,
            lu.`IsTop`,
            lu.`TopTime`,
            lu.`IsRead`,
            lu.`TableListQueryConfigId`,
            lu.`TableAggregateQueryConfigId`,
            lu.`LastViewDataTime`,
            lu.`LedgerCollection`
            FROM `Ledger_LedgerUsers` lu
            INNER JOIN `AbpUsers` u ON lu.`UserId` = u.`Id`
            WHERE lu.`LedgerId` = @LedgerId
            AND lu.`DepartmentId` = @DepartmentId
            AND FIND_IN_SET(@Permissions, lu.`Permissions`)
            AND u.`IsActive` = true
            AND u.`IsDeleted` = false";

        var parameters = new
        {
            LedgerId = ledgerId,
            DepartmentId = department,
            Permissions = (int)LedgerPermissionType.Approve
        };

        return (await connection.QueryAsync<LedgerUser>(sql, parameters)).ToList();
    }

    public override async Task<LedgerUserDepartmentView> GetSingleUserDepartments(int skipCount)
    {
        var connection = await GetDbConnectionAsync();

        string sql =
            @$"SELECT pu.`Id` as `UserId`, pd.`Id` as `DepartmentId`, pd.`DepartmentExtendId` as `LargeDepartmentId`
FROM `Platform_Users` pu
INNER JOIN `Platform_UserDepartments` pud ON pud.`UserId` = pu.`Id`
INNER JOIN `Platform_Departments` pd ON pud.`DepartmentId` = pd.`Id`
INNER JOIN `Ledger_LedgerUsers` llu ON llu.`UserId` = pu.`Id`
WHERE pu.`IsDeleted` = false 
  AND pd.`IsDeleted` = false 
  AND pd.`DepartmentExtendId` IS NOT NULL
GROUP BY pu.`Id`, pd.`Id`, pd.`DepartmentExtendId` LIMIT 1 OFFSET {skipCount}";

        return await connection.QueryFirstOrDefaultAsync<LedgerUserDepartmentView>(sql);
    }

    /// <summary>
    /// 管理员统计 发布部门不同层级的台账数量
    /// </summary>
    /// <param name="grade"></param>
    /// <returns></returns>
    public override async Task<int> GetLedgerCount(RegionGrade grade)
    {
        var connection = await GetDbConnectionAsync();
        var sql = @"SELECT COUNT(DISTINCT ll.`Id`) FROM `Ledger_Ledgers` ll 
                    INNER JOIN `Ledger_LedgerTypes` llt 
                    ON ll.`LedgerTypeId` = llt.`Id`
                    INNER JOIN `Platform_Departments` pd 
                    ON llt.`DepartmentId` = pd.`Id` 
                    INNER JOIN `Platform_Regions` pr 
                    ON pd.`RegionId` = pr.`Id` 
                    WHERE ll.`IsDeleted` != true
                    AND ll.`IsOnline` = true
                    AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
                    AND llt.`IsDeleted` != true
                    AND pd.`IsDeleted` != true
                    AND pr.`IsDeleted` != true
                    AND pr.`Grade` = @GradeId;";

        var parameters = new
        {
            GradeId = grade
        };
        return await connection.ExecuteScalarAsync<int>(sql, parameters);
    }

    /// <summary>
    /// 区县运维员统计 发布部门不同层级的台账数量(下钻)
    /// </summary>
    /// <param name="grade"></param>
    /// <param name="regionIds"></param>
    /// <param name="bigDepartmentIds"></param>
    /// <returns></returns>
    public override async Task<int> GetLedgerCount(RegionGrade grade, List<Guid> regionIds, List<Guid> bigDepartmentIds)
    {
        var connection = await GetDbConnectionAsync();
        var sql = @"SELECT COUNT(DISTINCT ll.`Id`) 
                    FROM `Ledger_Ledgers` ll 
                    INNER JOIN `Ledger_LedgerUsers` llu 
                    ON ll.`Id` = llu.`LedgerId` 
                    INNER JOIN `Platform_Departments` pd2 
                    ON llu.`DepartmentId` = pd2.`Id` 
                    INNER JOIN `Ledger_LedgerTypes` llt 
                    ON ll.`LedgerTypeId` = llt.`Id`
                    INNER JOIN `Platform_Departments` pd 
                    ON llt.`DepartmentId` = pd.`Id` 
                    INNER JOIN `Platform_Regions` pr 
                    ON pd.`RegionId` = pr.`Id` 
                    WHERE ll.`IsDeleted` != true
                      AND ll.`IsOnline` = true
                      AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
                      AND pd.`IsDeleted` != true
                      AND pr.`IsDeleted` != true
                      AND pd2.`IsDeleted` != true
                      AND llt.`IsDeleted` != true
                      AND pr.`Grade` = @GradeId
                      AND (llu.`DepartmentId` IN @BigDepartmentIds
                      OR pd2.`RegionId` IN @RegionIds);";

        var parameters = new
        {
            GradeId = grade,
            RegionIds = regionIds?.Any() == true
                ? (object)regionIds.ToArray()
                : DBNull.Value,
            BigDepartmentIds = bigDepartmentIds?.Any() == true
                ? (object)bigDepartmentIds.ToArray()
                : DBNull.Value
        };
        return await connection.ExecuteScalarAsync<int>(sql, parameters);
    }

    /// <summary>
    /// 区县运维员统计 发布部门不同层级的台账数量(不下钻)
    /// </summary>
    /// <param name="grade"></param>
    /// <param name="regionId"></param>
    /// <returns></returns>
    public override async Task<int> GetNoRunningInLedgerCount(RegionGrade? grade, Guid? regionId)
    {
        var connection = await GetDbConnectionAsync();
        var sql = @"SELECT COUNT(DISTINCT ll.`Id`)
            FROM `Ledger_Ledgers` ll 
            INNER JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId`
            INNER JOIN `Platform_Departments` pd ON lld.`DepartmentId` = pd.`Id` 
            INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id` 
            INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
            INNER JOIN `Platform_Departments` pd2 ON llt.`DepartmentId` = pd2.`Id`
            INNER JOIN `Platform_Regions` pr2 ON pd2.`RegionId` = pr2.`Id` 
            WHERE ll.`IsDeleted` = false
            AND llt.`IsDeleted` != true
            AND pd.`IsDeleted` != true 
            AND pr.`IsDeleted` != true
            AND pd2.`IsDeleted` != true
            AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
            AND ll.`Name` NOT LIKE '%【测试】%'
            AND ll.`IsOnline` = TRUE";

        if (grade.HasValue)
        {
            sql += " AND pr2.`Grade` = @Grade";
        }

        if (regionId.HasValue)
        {
            sql += " AND pd.`RegionId` = @RegionId";
        }

        var parameters = new
        {
            Grade = grade,
            RegionId = regionId
        };
        return await connection.ExecuteScalarAsync<int>(sql, parameters);
    }


    /// <summary>
    /// 管理员 查看市级统计
    /// </summary>
    /// <param name="grade"></param>
    /// <param name="take"></param>
    /// <param name="skip"></param>
    /// <param name="filter"></param>
    /// <returns></returns>
    public override async Task<(int, List<LedgerListByLedgerTypeView>)> GetAdminLedgerListByCity(RegionGrade grade,
        int take, int skip, string filter)
    {
        var connection = await GetDbConnectionAsync();
        var sqlBuilder = new StringBuilder(@"
                SELECT 
                    llt.`Id` AS LedgerTypeId,
                    llt.`Name` AS LedgerTypeName,
                    llt.`BindDepartmentId` AS DepartmentId,
                    COUNT(DISTINCT ll.`Id`) AS LedgersCount
                FROM `Ledger_Ledgers` ll
                INNER JOIN `Ledger_LedgerTypes` llt 
                    ON ll.`LedgerTypeId` = llt.`Id`
                INNER JOIN `Platform_Departments` pd 
                    ON llt.`DepartmentId` = pd.`Id`
                INNER JOIN `Platform_Regions` pr 
                    ON pd.`RegionId` = pr.`Id` 
                WHERE 
                    ll.`IsDeleted` != true
                    AND ll.`IsOnline` = true
                    AND ll.`Name` NOT LIKE '%【测试】%'
                    AND llt.`IsDeleted` != true
                    AND pd.`IsDeleted` != true
                    AND pr.`IsDeleted` != true
                    AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
                    AND pr.`Grade` = @Grade");

        if (filter != null)
        {
            sqlBuilder.AppendLine(" AND llt.`Name` LIKE @Filter"); // 使用 LIKE 操作符进行模糊匹配
        }

        sqlBuilder.Append(@"
                GROUP BY llt.`Id`
                HAVING 
                    COUNT(DISTINCT ll.`Id`) > 0
                ORDER BY llt.`Id`
                LIMIT @Skip, @Take;");


        var sqlCountBuilder = new StringBuilder(@"
            SELECT 
                COUNT(*) as total_count
            FROM (
                SELECT 
                    llt.`Id` AS LedgerTypeId,
                    llt.`Name` AS LedgerTypeName,
                    llt.`DepartmentId` AS DepartmentId,
                    COUNT(DISTINCT ll.`Id`) AS LedgersCount
                FROM `Ledger_Ledgers` ll
                INNER JOIN `Ledger_LedgerTypes` llt 
                    ON ll.`LedgerTypeId` = llt.`Id`
                INNER JOIN `Platform_Departments` pd 
                    ON llt.`DepartmentId` = pd.`Id`
                INNER JOIN `Platform_Regions` pr 
                    ON pd.`RegionId` = pr.`Id` 
                WHERE 
                    ll.`IsDeleted` != true
                    AND ll.`IsOnline` = true
                    AND ll.`Name` NOT LIKE '%【测试】%'
                    AND llt.`IsDeleted` != true
                    AND pd.`IsDeleted` != true
                    AND pr.`IsDeleted` != true
                    AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
                    AND pr.`Grade` = @Grade");

        if (filter != null)
        {
            sqlCountBuilder.AppendLine(" AND llt.`Name` LIKE @Filter"); // 使用 LIKE 操作符进行模糊匹配
        }

        sqlCountBuilder.Append(@"
                GROUP BY llt.`Id`
                HAVING 
                    COUNT(DISTINCT ll.`Id`) > 0
                ORDER BY llt.`Id`
            )");

        var parameters = new
        {
            Grade = grade,
            Take = take,
            Skip = skip,
            Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null,
        };
        var totalCount = await connection.ExecuteScalarAsync<int>(sqlCountBuilder.ToString(), parameters);
        var result =
            (await connection.QueryAsync<LedgerListByLedgerTypeView>(sqlBuilder.ToString(), parameters)).ToList();
        return (totalCount, result);
    }

    public override async Task<(int, List<LedgerTableBindDepartment>)> GetAdminBindDepartmentTableByCity(
        RegionGrade grade,
        int take, int skip, string filter, bool? isDesc = null)
    {
        var connection = await GetDbConnectionAsync();
        var sqlBuilder = new StringBuilder(@" 
        WITH ranked_ledgers AS (
            SELECT DISTINCT
                llt.`Id` AS `LedgerTypeId`,
                COUNT(ll.`Id`) OVER (PARTITION BY llt.`Id`) as ledger_count
            FROM `Ledger_Ledgers` ll 
            INNER JOIN `Ledger_LedgerTypes` llt 
                ON ll.`LedgerTypeId` = llt.`Id` 
            INNER JOIN `Platform_Departments` pd 
                ON llt.`DepartmentId` = pd.`Id` 
            INNER JOIN `Platform_Regions` pr 
                ON pd.`RegionId` = pr.`Id` 
            WHERE 
                ll.`IsDeleted` != true 
                AND ll.`IsOnline` = true 
                AND ll.`Name` NOT LIKE '%【测试】%' 
                AND llt.`IsDeleted` != true 
                AND pd.`IsDeleted` != true 
                AND pr.`IsDeleted` != true 
                AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1) 
                AND pr.`Grade` = @Grade");

        if (filter != null)
        {
            sqlBuilder.AppendLine(" AND llt.`Name` LIKE @Filter");
        }

        if (isDesc.HasValue)
        {
            var order = isDesc.Value ? "DESC" : "ASC";
            sqlBuilder.AppendLine($" ORDER BY ledger_count {order}, `LedgerTypeId`");
        }
        else
        {
            sqlBuilder.AppendLine($" ORDER BY `LedgerTypeId`");
        }
            
        sqlBuilder.Append(@" 
            LIMIT @Skip, @Take
        )");
        
        sqlBuilder.Append(@"SELECT 
            ll.`Id` AS `LedgerId`, 
            llt.`BindDepartmentId` AS `DepartmentId`, 
            lti.`Name` AS `TableName`, 
            llt.`Id` AS `LedgerTypeId`, 
            llt.`Name` AS `LedgerTypeName` 
        FROM ranked_ledgers r
        INNER JOIN `Ledger_LedgerTypes` llt ON llt.`Id` = r.`LedgerTypeId`
        INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id`
        INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id` 
        INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id` 
        INNER JOIN `Ledger_TableInfos` lti ON ll.`Id` = lti.`Id` 
        WHERE 
            ll.`IsDeleted` != true 
            AND ll.`IsOnline` = true 
            AND ll.`Name` NOT LIKE '%【测试】%' 
            AND llt.`IsDeleted` != true 
            AND pd.`IsDeleted` != true 
            AND pr.`IsDeleted` != true 
            AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1) 
            AND pr.`Grade` = @Grade");

        if (filter != null)
        {
            sqlBuilder.AppendLine(" AND llt.`Name` LIKE @Filter");
        }

        sqlBuilder.Append(@" 
        ORDER BY llt.`Id`;");

        var sqlCountBuilder = new StringBuilder(@" 
        SELECT COUNT(DISTINCT llt.`Id`)
        FROM `Ledger_Ledgers` ll 
        INNER JOIN `Ledger_LedgerTypes` llt 
            ON ll.`LedgerTypeId` = llt.`Id` 
        INNER JOIN `Platform_Departments` pd 
            ON llt.`DepartmentId` = pd.`Id` 
        INNER JOIN `Platform_Regions` pr 
            ON pd.`RegionId` = pr.`Id` 
        WHERE 
            ll.`IsDeleted` != true 
            AND ll.`IsOnline` = true 
            AND ll.`Name` NOT LIKE '%【测试】%' 
            AND llt.`IsDeleted` != true 
            AND pd.`IsDeleted` != true 
            AND pr.`IsDeleted` != true 
            AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1) 
            AND pr.`Grade` = @Grade");

        if (filter != null)
        {
            sqlCountBuilder.AppendLine(" AND llt.`Name` LIKE @Filter");
        }

        var parameters = new
        {
            Grade = grade,
            Take = take,
            Skip = skip,
            Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null,
        };

        var totalCount = await connection.ExecuteScalarAsync<int>(sqlCountBuilder.ToString(), parameters);
        var result =
            (await connection.QueryAsync<LedgerTableBindDepartment>(sqlBuilder.ToString(), parameters)).ToList();
        return (totalCount, result);
    }

    /// <summary>
    /// 管理员 查看区县统计（已优化Sql）
    /// </summary>
    /// <param name="take"></param>
    /// <param name="skip"></param>
    /// <param name="filter"></param>
    /// <param name="regionId"></param>
    /// <param name="isDesc"></param>
    /// <returns></returns>
    public override async Task<(int, List<LedgerListByDistrictRegionView>)> GetLedgerListByDistrictRegion(int take,
        int skip, string filter, Guid? regionId, bool? isDesc = null)
    {
        var connection = await GetDbConnectionAsync();

        // 构建基础SQL查询
        var baseFilterClause = !string.IsNullOrEmpty(filter) ? " AND pr.`Name` LIKE @Filter" : "";
        var baseCityFilterClause = !string.IsNullOrEmpty(filter) ? " AND apr.`Name` LIKE @Filter" : "";

        // 使用单一CTE查询构建数据和计数查询
        var sqlTemplate = @"
        WITH ledgerTypeData AS (
            SELECT 
                pr.`Id` AS RegionId,
                pr.`Name` AS RegionName,
                COUNT(DISTINCT ll.`Id`) AS LedgersCount  
            FROM `Ledger_Ledgers` ll
            INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
            INNER JOIN `Platform_Departments` pd ON llt.`BindDepartmentId` = pd.`Id`
            INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id`
            WHERE 
                pr.`Grade` = 3
                AND ll.`IsOnline` = true 
                AND ll.`IsDeleted` = false 
                AND pd.`IsDeleted` = false
                AND pr.`IsDeleted` = false
                AND llt.`IsDeleted` = false
                AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
                AND ll.`Name` NOT LIKE '%【测试】%'
                {0}
            GROUP BY pr.`Id`
        ),
        cityLedgerData AS (
            SELECT 
                apr.`Id` AS RegionId,
                apr.`Name` AS RegionName,
                COUNT(DISTINCT ll.`Id`) AS LedgersCount  
            FROM `Ledger_Ledgers` ll
            INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id` 
            INNER JOIN `Platform_Departments` pd ON llt.`BindDepartmentId` = pd.`Id` 
            INNER JOIN `Platform_Regions` pr3 ON pd.`RegionId` = pr3.`Id` 
            INNER JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId`
            INNER JOIN `Platform_Departments` lpd ON lld.`AuthDepartmentId` = lpd.`Id`
            INNER JOIN `Platform_Regions` lpr ON lpd.`RegionId` = lpr.`Id`
            INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id`
            INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id`
            WHERE 
                lpr.`Grade` = 2
                AND apr.`Grade` = 3
                AND ll.`IsOnline` = true 
                AND ll.`IsDeleted` = false 
                AND lpr.`IsDeleted` = false
                AND apr.`IsDeleted` = false
                AND apd.`IsDeleted` = false
                AND lpd.`IsDeleted` = false
                AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
                AND ll.`Name` NOT LIKE '%【测试】%'
                AND pr3.`Grade` = 2
                {1}
            GROUP BY apr.`Id`
        ),
        combined_data AS (
            SELECT 
                COALESCE(ltd.RegionId, cld.RegionId) AS RegionId,
                COALESCE(ltd.RegionName, cld.RegionName) AS RegionName,
                COALESCE(ltd.LedgersCount, 0) AS PublishCount,
                COALESCE(cld.LedgersCount, 0) AS ReceivingCount,
                COALESCE(ltd.LedgersCount, 0) + COALESCE(cld.LedgersCount, 0) AS LedgersCount
            FROM ledgerTypeData ltd
            LEFT JOIN cityLedgerData cld ON ltd.RegionId = cld.RegionId
            UNION
            SELECT 
                cld.RegionId,
                cld.RegionName,
                0 AS PublishCount,
                cld.LedgersCount AS ReceivingCount,
                cld.LedgersCount AS LedgersCount
            FROM cityLedgerData cld
            LEFT JOIN ledgerTypeData ltd ON cld.RegionId = ltd.RegionId
            WHERE ltd.RegionId IS NULL
        )";

        // 构建数据查询
        var sqlBuilder = new StringBuilder(string.Format(sqlTemplate, baseFilterClause, baseCityFilterClause));
        sqlBuilder.AppendLine(@"
        SELECT 
            RegionId,
            RegionName,
            PublishCount,
            ReceivingCount,
            LedgersCount
        FROM combined_data
        WHERE LedgersCount > 0");

        if (regionId.HasValue)
        {
            sqlBuilder.Append(@" AND RegionId = @RegionId");
        }

        if (isDesc.HasValue)
        {
            var orderSql = isDesc.Value ? " DESC" : " ASC";
            sqlBuilder.AppendLine($" ORDER BY LedgersCount {orderSql}");
            sqlBuilder.AppendLine(@"
            LIMIT @Skip, @Take");
        }
        else
        {
            sqlBuilder.AppendLine(@"
            ORDER BY RegionId
            LIMIT @Skip, @Take");
        }
        
        // 构建计数查询
        var sqlCountBuilder = new StringBuilder(string.Format(sqlTemplate, baseFilterClause, baseCityFilterClause));
        sqlCountBuilder.AppendLine(@"
        SELECT COUNT(*) 
        FROM combined_data
        WHERE LedgersCount > 0");

        if (regionId.HasValue)
        {
            sqlCountBuilder.Append(@" AND RegionId = @RegionId");
        }

        // 定义参数
        var parameters = new
        {
            Take = take,
            Skip = skip,
            Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null,
            RegionId = regionId
        };

        // 串行执行查询，避免并发问题
        var totalCount = await connection.ExecuteScalarAsync<int>(sqlCountBuilder.ToString(), parameters);
        var result = (await connection.QueryAsync<LedgerListByDistrictRegionView>(sqlBuilder.ToString(), parameters))
            .ToList();

        return (totalCount, result);
    }
    
    /// <summary>
    /// 区县运维员  各区级统计（已优化Sql）
    /// </summary>
    /// <param name="grade"></param>
    /// <param name="take"></param>
    /// <param name="skip"></param>
    /// <param name="regionId"></param>
    /// <param name="filter"></param>
    /// <returns></returns>
    public override async Task<(int, List<LedgerListByLedgerTypeView>)> GetCountiesLedgerListByRegion(RegionGrade grade,
        int take, int skip, Guid regionId, string filter)
    {
        var connection = await GetDbConnectionAsync();

        // 使用CTE优化查询
        var filterClause = !string.IsNullOrEmpty(filter) ? " AND llt.`Name` LIKE @Filter" : "";

        var baseSql = $@"
        WITH filtered_ledgers AS (
            SELECT DISTINCT
                ll.`Id` AS LedgerId,
                llt.`Id` AS LedgerTypeId,
                llt.`BindDepartmentId` AS DepartmentId,
                llt.`Name` AS LedgerTypeName
            FROM `Ledger_Ledgers` ll
            LEFT JOIN `Ledger_LedgerUsers` llu ON ll.`Id` = llu.`LedgerId`
            LEFT JOIN `Platform_Departments` pd2 ON llu.`DepartmentId` = pd2.`Id`
            INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
            INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
            INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id`
            WHERE ll.`IsDeleted` = false
                AND ll.`Name` NOT LIKE '%【测试】%'
                AND ll.`IsOnline` = true
                AND llt.`IsDeleted` = false
                AND pd.`IsDeleted` = false
                AND pr.`IsDeleted` = false
                AND (pd2.`IsDeleted` = false OR pd2.`Id` IS NULL)
                AND (pr.`Grade` = 3 OR llu.`UserId` IS NULL)
                AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
                AND pr.`Id` = @RegionId
                {filterClause}
        )
        ";

        // 构建数据查询
        var sqlBuilder = new StringBuilder(baseSql);
        sqlBuilder.Append(@"
        SELECT 
            LedgerTypeId,
            LedgerTypeName,
            DepartmentId,
            COUNT(DISTINCT LedgerId) AS LedgersCount
        FROM filtered_ledgers
        GROUP BY LedgerTypeId, LedgerTypeName
        HAVING 
            COUNT(DISTINCT LedgerId) > 0
        ORDER BY LedgersCount DESC
        LIMIT @Take
        OFFSET @Skip;
        ");

        // 构建计数查询
        var sqlCountBuilder = new StringBuilder(baseSql);
        sqlCountBuilder.Append(@"
        SELECT COUNT(*) FROM (
            SELECT LedgerTypeId
            FROM filtered_ledgers
            GROUP BY LedgerTypeId, LedgerTypeName
        ) AS count_query;
        ");

        // 定义参数
        var parameters = new
        {
            Grade = grade,
            Take = take,
            Skip = skip,
            RegionId = regionId,
            Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null
        };

        // 串行执行查询，避免并发问题
        var totalCount = await connection.ExecuteScalarAsync<int>(sqlCountBuilder.ToString(), parameters);
        var result =
            (await connection.QueryAsync<LedgerListByLedgerTypeView>(sqlBuilder.ToString(), parameters)).ToList();

        return (totalCount, result);
    }

    public override async Task<(int, List<LedgerTableBindDepartment>)> GetCountiesBindDepartmentTableByRegion(
        RegionGrade grade,
        int take, int skip, Guid regionId, string filter, bool? isDesc = null)
    {
        var connection = await GetDbConnectionAsync();

        var filterClause = !string.IsNullOrEmpty(filter) ? " AND llt.`Name` LIKE @Filter" : "";

        var baseSql = $@" 
WITH ledger_types AS (
    SELECT DISTINCT llt.`Id` AS `LedgerTypeId`
    FROM `Ledger_LedgerTypes` llt
    INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id`
    INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
    INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id`
    WHERE llt.`IsDeleted` = false
    AND ll.`IsDeleted` = false
    AND pd.`IsDeleted` = false
    AND pr.`IsDeleted` = false
    AND pr.`Id` = @RegionId
    AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
    AND ll.`Name` NOT LIKE '%【测试】%'
    AND ll.`IsOnline` = true
    {filterClause}
    ORDER BY `LedgerTypeId`
    LIMIT @Skip, @Take
),
filtered_ledgers AS ( 
    SELECT DISTINCT 
        ll.`Id` AS `LedgerId`, 
        llt.`BindDepartmentId` AS `DepartmentId`, 
        lti.`Name` AS `TableName`, 
        llt.`Id` AS `LedgerTypeId`, 
        llt.`Name` AS `LedgerTypeName` 
    FROM ledger_types lt
    INNER JOIN `Ledger_LedgerTypes` llt ON lt.`LedgerTypeId` = llt.`Id`
    INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id` 
    LEFT JOIN `Ledger_LedgerUsers` llu ON ll.`Id` = llu.`LedgerId` 
    LEFT JOIN `Platform_Departments` pd2 ON llu.`DepartmentId` = pd2.`Id` 
    INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id` 
    INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id` 
    INNER JOIN `Ledger_TableInfos` lti ON ll.`Id` = lti.`Id` 
    WHERE ll.`IsDeleted` = false 
    AND ll.`Name` NOT LIKE '%【测试】%' 
    AND ll.`IsOnline` = true 
    AND llt.`IsDeleted` = false 
    AND pd.`IsDeleted` = false 
    AND pr.`IsDeleted` = false 
    AND (pd2.`IsDeleted` = false OR pd2.`Id` IS NULL) 
    AND (pr.`Grade` = 3 OR llu.`UserId` IS NULL) 
    AND pr.`Id` = @RegionId
),
ledger_counts AS (
    SELECT 
        `LedgerTypeId`,
        COUNT(DISTINCT `LedgerId`) as ledger_count
    FROM filtered_ledgers
    GROUP BY `LedgerTypeId`
)
SELECT DISTINCT
    fl.`DepartmentId`, 
    fl.`LedgerId`, 
    fl.`TableName`, 
    fl.`LedgerTypeId`, 
    fl.`LedgerTypeName`,
    lc.ledger_count  -- 添加这个字段到SELECT列表中
FROM filtered_ledgers fl
INNER JOIN ledger_counts lc ON fl.`LedgerTypeId` = lc.`LedgerTypeId`";
    
        if (isDesc.HasValue)
        {
            var order = isDesc.Value ? "DESC" : "ASC";
            baseSql += $" ORDER BY lc.`ledger_count` {order}, `LedgerTypeId`";
        }
        else
        {
            baseSql += $" ORDER BY fl.`LedgerTypeId`";
        }

        var countSql = $@" 
    WITH filtered_ledger_types AS (
        SELECT DISTINCT llt.`Id` AS `LedgerTypeId`
        FROM `Ledger_LedgerTypes` llt
        INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id`
        INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
        INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id`
        WHERE llt.`IsDeleted` = false
        AND ll.`IsDeleted` = false
        AND pd.`IsDeleted` = false
        AND pr.`IsDeleted` = false
        AND pr.`Id` = @RegionId
        AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
        AND ll.`Name` NOT LIKE '%【测试】%'
        AND ll.`IsOnline` = true
        {filterClause}
    ),
    filtered_ledgers_count AS (
        SELECT COUNT(DISTINCT flt.`LedgerTypeId`)
        FROM filtered_ledger_types flt
        INNER JOIN `Ledger_LedgerTypes` llt ON flt.`LedgerTypeId` = llt.`Id`
        INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id`
        LEFT JOIN `Ledger_LedgerUsers` llu ON ll.`Id` = llu.`LedgerId`
        LEFT JOIN `Platform_Departments` pd2 ON llu.`DepartmentId` = pd2.`Id`
        INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
        INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id`
        INNER JOIN `Ledger_TableInfos` lti ON ll.`Id` = lti.`Id`
        WHERE ll.`IsDeleted` = false
        AND ll.`IsOnline` = true
        AND ll.`Name` NOT LIKE '%【测试】%'
        AND llt.`IsDeleted` = false
        AND pd.`IsDeleted` = false
        AND pr.`IsDeleted` = false
        AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
        AND (pd2.`IsDeleted` = false OR pd2.`Id` IS NULL) 
        AND (pr.`Grade` = 3 OR llu.`UserId` IS NULL) 
        AND pr.`Id` = @RegionId
    )
    SELECT * FROM filtered_ledgers_count";

        var parameters = new
        {
            Grade = grade,
            Take = take,
            Skip = skip,
            RegionId = regionId,
            Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null
        };

        var totalCount = await connection.ExecuteScalarAsync<int>(countSql, parameters);
        var result =
            (await connection.QueryAsync<LedgerTableBindDepartment>(baseSql, parameters)).ToList();

        return (totalCount, result);
    }

    /// <summary>
    /// 区县运维员  各市级统计（已优化Sql）
    /// </summary>
    /// <param name="grade">区域等级</param>
    /// <param name="take">获取记录数</param>
    /// <param name="skip">跳过记录数</param>
    /// <param name="regionId">区域ID</param>
    /// <param name="filter">过滤条件</param>
    /// <returns>返回总数和结果列表</returns>
    public override async Task<(int, List<LedgerTableBindDepartment>)> GetBindDepartmentTableByRegion(RegionGrade grade,
        int take, int skip, Guid regionId, string filter, bool? isDesc = null)
    {
        var connection = await GetDbConnectionAsync();

        string sql = @" 
WITH ledger_types AS (
    SELECT DISTINCT llt.`Id` AS `LedgerTypeId`
    FROM `Ledger_LedgerTypes` llt
    INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id`
    INNER JOIN `Platform_Departments` pd ON llt.`BindDepartmentId` = pd.`Id` 
    INNER JOIN `Platform_Regions` pr3 ON pd.`RegionId` = pr3.`Id`
    INNER JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId`
    INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id`
    INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id`
    WHERE llt.`IsDeleted` = 0
    AND ll.`IsDeleted` = 0
    AND pd.`IsDeleted` = 0
    AND apd.`IsDeleted` = 0
    AND apr.`IsDeleted` = 0
    AND pr3.`Grade` = 2
    AND apr.`Id` = @RegionId
    AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
    AND ll.`Name` NOT LIKE '%【测试】%'
    AND ll.`IsOnline` = 1";

        if (!string.IsNullOrEmpty(filter))
        {
            sql += "\n        AND llt.`Name` LIKE @Filter";
        }

        sql += $" ORDER BY `LedgerTypeId`";
    
        sql += @"
    LIMIT @Skip, @Take
),
filtered_ledgers AS ( 
    SELECT DISTINCT 
        ll.`Id` AS `LedgerId`, 
        llt.`BindDepartmentId` AS `DepartmentId`, 
        lti.`Name` AS `TableName`, 
        llt.`Id` AS `LedgerTypeId`, 
        llt.`Name` AS `LedgerTypeName`
    FROM ledger_types lt
    INNER JOIN `Ledger_LedgerTypes` llt ON lt.`LedgerTypeId` = llt.`Id`
    INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id` 
    INNER JOIN `Platform_Departments` pd ON llt.`BindDepartmentId` = pd.`Id` 
    INNER JOIN `Platform_Regions` pr3 ON pd.`RegionId` = pr3.`Id` 
    INNER JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId` 
    INNER JOIN `Platform_Departments` lpd ON lld.`AuthDepartmentId` = lpd.`Id` 
    INNER JOIN `Platform_Regions` lpr ON lpd.`RegionId` = lpr.`Id` 
    INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id` 
    INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id` 
    INNER JOIN `Ledger_TableInfos` lti ON ll.`Id` = lti.`Id` 
    WHERE ll.`IsDeleted` = 0 
    AND llt.`IsDeleted` = 0 
    AND lpd.`IsDeleted` = 0 
    AND lpr.`IsDeleted` = 0 
    AND apd.`IsDeleted` = 0 
    AND apr.`IsDeleted` = 0 
    AND lpr.`Grade` = 2 
    AND apr.`Grade` = 3 
    AND pr3.`Grade` = 2 
    AND ll.`Name` NOT LIKE '%【测试】%' 
    AND ll.`IsOnline` = 1
    AND apr.`Id` = @RegionId
),
ledger_counts AS (
    SELECT 
        `LedgerTypeId`,
        COUNT(DISTINCT `LedgerId`) as ledger_count
    FROM filtered_ledgers
    GROUP BY `LedgerTypeId`
)
SELECT 
    fl.`DepartmentId`, 
    fl.`LedgerId`, 
    fl.`TableName`, 
    fl.`LedgerTypeId`, 
    fl.`LedgerTypeName`,
    lc.ledger_count
FROM filtered_ledgers fl 
INNER JOIN ledger_counts lc ON fl.`LedgerTypeId` = lc.`LedgerTypeId`";
    
        if (isDesc.HasValue)
        {
            var order = isDesc.Value ? "DESC" : "ASC";
            sql += $" ORDER BY lc.ledger_count {order}, fl.`LedgerTypeId`";
        }
        else
        {
            sql += $" ORDER BY fl.`LedgerTypeId`";
        }

        string countSql = @" 
WITH filtered_ledger_types AS (
    SELECT DISTINCT llt.`Id` AS `LedgerTypeId`
    FROM `Ledger_LedgerTypes` llt
    INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id`
    INNER JOIN `Platform_Departments` pd ON llt.`BindDepartmentId` = pd.`Id` 
    INNER JOIN `Platform_Regions` pr3 ON pd.`RegionId` = pr3.`Id`
    INNER JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId`
    INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id`
    INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id`
    WHERE llt.`IsDeleted` = 0
    AND ll.`IsDeleted` = 0
    AND pd.`IsDeleted` = 0
    AND apd.`IsDeleted` = 0
    AND apr.`IsDeleted` = 0
    AND pr3.`Grade` = 2
    AND apr.`Id` = @RegionId
    AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
    AND ll.`Name` NOT LIKE '%【测试】%'
    AND ll.`IsOnline` = 1";

        if (!string.IsNullOrEmpty(filter))
        {
            countSql += "\n        AND llt.`Name` LIKE @Filter";
        }
        
        countSql += @"
    ),
    filtered_ledgers_count AS (
        SELECT COUNT(DISTINCT flt.`LedgerTypeId`)
        FROM filtered_ledger_types flt
        INNER JOIN `Ledger_LedgerTypes` llt ON flt.`LedgerTypeId` = llt.`Id`
        INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id` 
        INNER JOIN `Platform_Departments` pd ON llt.`BindDepartmentId` = pd.`Id` 
        INNER JOIN `Platform_Regions` pr3 ON pd.`RegionId` = pr3.`Id` 
        INNER JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId` 
        INNER JOIN `Platform_Departments` lpd ON lld.`AuthDepartmentId` = lpd.`Id` 
        INNER JOIN `Platform_Regions` lpr ON lpd.`RegionId` = lpr.`Id` 
        INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id` 
        INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id` 
        INNER JOIN `Ledger_TableInfos` lti ON ll.`Id` = lti.`Id` 
        WHERE ll.`IsDeleted` = 0 
        AND llt.`IsDeleted` = 0 
        AND lpd.`IsDeleted` = 0 
        AND lpr.`IsDeleted` = 0 
        AND apd.`IsDeleted` = 0 
        AND apr.`IsDeleted` = 0 
        AND lpr.`Grade` = 2 
        AND apr.`Grade` = 3 
        AND pr3.`Grade` = 2 
        AND ll.`Name` NOT LIKE '%【测试】%' 
        AND ll.`IsOnline` = 1
        AND apr.`Id` = @RegionId
    )
    SELECT * FROM filtered_ledgers_count";

        var parameters = new
        {
            Grade = grade,
            Take = take,
            Skip = skip,
            RegionId = regionId,
            Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null
        };

        var totalCount = await connection.ExecuteScalarAsync<int>(countSql, parameters);
        var result = (await connection.QueryAsync<LedgerTableBindDepartment>(sql, parameters)).ToList();

        return (totalCount, result);
    }


    /// <summary>
    /// 区县运维员  各市级统计（已优化Sql）
    /// </summary>
    /// <param name="grade">区域等级</param>
    /// <param name="take">获取记录数</param>
    /// <param name="skip">跳过记录数</param>
    /// <param name="regionId">区域ID</param>
    /// <param name="filter">过滤条件</param>
    /// <returns>返回总数和结果列表</returns>
    public override async Task<(int, List<LedgerListByCityRegionView>)> GetCityLedgerListByRegion(RegionGrade grade,
        int take, int skip, Guid regionId, string filter)
    {
        var connection = await GetDbConnectionAsync();

        // 构建基础SQL查询
        string baseSql = @"
        WITH filtered_ledgers AS (
            SELECT DISTINCT 
                ll.`Id` AS `LedgerId`, 
                llt.`Id` AS `LedgerTypeId`, 
                llt.`BindDepartmentId` AS DepartmentId, 
                llt.`Name` AS `LedgerTypeName`
            FROM `Ledger_Ledgers` ll 
            INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id` 
            INNER JOIN `Platform_Departments` pd ON llt.`BindDepartmentId` = pd.`Id` 
            INNER JOIN `Platform_Regions` pr3 ON pd.`RegionId` = pr3.`Id` 
            INNER JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId` 
            INNER JOIN `Platform_Departments` lpd ON lld.`AuthDepartmentId` = lpd.`Id` 
            INNER JOIN `Platform_Regions` lpr ON lpd.`RegionId` = lpr.`Id` 
            INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id` 
            INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id` 
            WHERE ll.`IsDeleted` = false 
            AND llt.`IsDeleted` = false 
            AND lpd.`IsDeleted` = false 
            AND lpr.`IsDeleted` = false 
            AND apd.`IsDeleted` = false 
            AND apr.`IsDeleted` = false 
            AND lpr.`Grade` = 2 
            AND apr.`Grade` = 3 
            AND pr3.`Grade` = 2 
            AND apr.`Id` = @RegionId
            AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1) 
            AND ll.`Name` NOT LIKE '%【测试】%' 
            AND ll.`IsOnline` = TRUE";

        // 动态添加过滤条件
        if (!string.IsNullOrEmpty(filter))
        {
            baseSql += "\n            AND llt.\"Name\" LIKE @Filter";
        }

        baseSql += @"
        ),
        valid_departments AS (
            SELECT DISTINCT 
                lld.`LedgerId`
            FROM `Ledger_LedgerDepartments` lld
            INNER JOIN `Platform_Departments` lpd ON lld.`AuthDepartmentId` = lpd.`Id`
            INNER JOIN `Platform_Regions` lpr ON lpd.`RegionId` = lpr.`Id`
            INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id`
            INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id`
            WHERE 
            apr.`Grade` = 2
        )";

        // 主查询SQL
        var sqlBuilder = new StringBuilder(baseSql);
        sqlBuilder.Append(@"
        SELECT 
            fl.`LedgerTypeId`, 
            fl.`LedgerTypeName`, 
            DepartmentId,
            COUNT(DISTINCT fl.`LedgerId`) AS LedgersCount 
        FROM filtered_ledgers fl
        LEFT JOIN valid_departments vd ON fl.`LedgerId` = vd.`LedgerId`
        GROUP BY fl.`LedgerTypeId`, fl.`LedgerTypeName`
        HAVING 
            COUNT(DISTINCT fl.`LedgerId`) > 0
        ORDER BY LedgersCount DESC
        LIMIT @Take 
        OFFSET @Skip;");

        // 计数查询SQL
        var sqlCountBuilder = new StringBuilder(baseSql);
        sqlCountBuilder.Append(@"
        SELECT COUNT(*) FROM (
            SELECT 
                fl.`LedgerTypeId`
            FROM filtered_ledgers fl
            INNER JOIN valid_departments vd ON fl.`LedgerId` = vd.`LedgerId`
            GROUP BY fl.`LedgerTypeId`, fl.`LedgerTypeName`
        ) AS count_query");

        var parameters = new
        {
            Grade = grade,
            Take = take,
            Skip = skip,
            RegionId = regionId,
            Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null
        };

        // 串行执行查询，避免并发问题
        var totalCount = await connection.ExecuteScalarAsync<int>(sqlCountBuilder.ToString(), parameters);
        var result =
            (await connection.QueryAsync<LedgerListByCityRegionView>(sqlBuilder.ToString(), parameters)).ToList();

        return (totalCount, result);
    }

    /// <summary>
    /// 管理员 查看市级部门 - 详情（已优化Sql）
    /// </summary>
    /// <param name="grade"></param>
    /// <param name="take"></param>
    /// <param name="skip"></param>
    /// <param name="filter"></param>
    /// <param name="ledgerTypeId"></param>
    /// <param name="runway"></param>
    /// <returns></returns>
    public override async Task<(int, List<LedgerDetailListView>)> GetAdminLedgerDetailListByCity(RegionGrade grade,
        int take, int skip, string filter, Guid ledgerTypeId, string runway)
    {
        var connection = await GetDbConnectionAsync();

        // 使用CTE优化查询
        var baseSql = @"
        WITH filtered_ledgers AS (
            SELECT 
                ll.`Id` AS LedgerId,
                ll.`Name` AS LedgerName,
                ll.`Runway` AS Runway,
                llt.`Name` AS LedgerTypeName
            FROM `Ledger_Ledgers` ll
            INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
            INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
            INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id` 
            WHERE ll.`IsDeleted` = false
                AND ll.`IsOnline` = true
                AND ll.`Name` NOT LIKE '%【测试】%'
                AND llt.`IsDeleted` = false
                AND pd.`IsDeleted` = false
                AND pr.`IsDeleted` = false
                AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
                AND pr.`Grade` = 2
                AND llt.`Id` = @LedgerTypeId";

        // 添加可选过滤条件
        var whereClause = new StringBuilder();
        if (!string.IsNullOrEmpty(filter))
        {
            whereClause.AppendLine(" AND ll.\"Name\" LIKE @Filter");
        }

        if (!string.IsNullOrEmpty(runway))
        {
            whereClause.AppendLine(" AND ll.\"Runway\" = @Runway");
        }

        // 完成CTE定义
        var completeCte = baseSql + whereClause + "\n        )";

        // 构建数据查询
        var sqlBuilder = new StringBuilder(completeCte);
        sqlBuilder.AppendLine(@"
        SELECT 
            LedgerId,
            LedgerName,
            Runway,
            LedgerTypeName
        FROM filtered_ledgers
        LIMIT @Take 
        OFFSET @Skip;");

        // 构建计数查询
        var sqlCountBuilder = new StringBuilder(completeCte);
        sqlCountBuilder.AppendLine(@"
        SELECT COUNT(*) 
        FROM filtered_ledgers;");

        // 定义参数
        var parameters = new
        {
            Grade = grade,
            Take = take,
            Skip = skip,
            Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null,
            LedgerTypeId = ledgerTypeId,
            Runway = runway
        };

        // 串行执行查询，避免并发问题
        var totalCount = await connection.ExecuteScalarAsync<int>(sqlCountBuilder.ToString(), parameters);
        var result = (await connection.QueryAsync<LedgerDetailListView>(sqlBuilder.ToString(), parameters)).ToList();

        return (totalCount, result);
    }


    /// <summary>
    /// 管理员 查看区县统计 - 详情（已优化SQL）
    /// </summary>
    /// <param name="take"></param>
    /// <param name="skip"></param>
    /// <param name="filter"></param>
    /// <param name="regionId"></param>
    /// <param name="runway"></param>
    /// <returns></returns>
    public override async Task<(int, List<LedgerDetailListView>)> GetAdminLedgerDetailListByRegion(int take,
        int skip, string filter, Guid regionId, string runway)
    {
        var connection = await GetDbConnectionAsync();

        // 构建动态过滤条件
        var filterCondition = !string.IsNullOrEmpty(filter) ? " AND ll.\"Name\" LIKE @Filter" : "";
        var runwayCondition = !string.IsNullOrEmpty(runway) ? " AND ll.\"Runway\" = @Runway" : "";

        // 使用公共基础SQL构建CTE
        var baseSql = $@"
        WITH ledgerTypeData AS (
            SELECT DISTINCT
                ll.`Id` AS LedgerId,
                ll.`Name` AS LedgerName,
                ll.`Runway` AS Runway,
                llt.`Name` AS LedgerTypeName
            FROM `Ledger_Ledgers` ll
            INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
            INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
            INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id`
            WHERE 
                pr.`Grade` = 3
                AND ll.`IsOnline` = TRUE 
                AND ll.`IsDeleted` = FALSE
                AND pd.`IsDeleted` = FALSE
                AND pr.`IsDeleted` = FALSE
                AND llt.`IsDeleted` = FALSE
                AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
                AND ll.`Name` NOT LIKE '%【测试】%'
                AND pr.`Id` = @RegionId
                {filterCondition}
                {runwayCondition}
        ),
        cityLedgerData AS (
            SELECT DISTINCT
                ll.`Id` AS LedgerId,
                ll.`Name` AS LedgerName,
                ll.`Runway` AS Runway,
                llt.`Name` AS LedgerTypeName
            FROM `Ledger_Ledgers` ll
            INNER JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId`
            INNER JOIN `Platform_Departments` lpd ON lld.`AuthDepartmentId` = lpd.`Id`
            INNER JOIN `Platform_Regions` lpr ON lpd.`RegionId` = lpr.`Id`
            INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id`
            INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id`
            INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
            WHERE 
                lpr.`Grade` = 2
                AND apr.`Grade` = 3
                AND ll.`IsOnline` = TRUE 
                AND ll.`IsDeleted` = FALSE
                AND lpr.`IsDeleted` = FALSE
                AND apr.`IsDeleted` = FALSE
                AND apd.`IsDeleted` = FALSE
                AND lpd.`IsDeleted` = FALSE
                AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
                AND ll.`Name` NOT LIKE '%【测试】%'
                AND apr.`Id` = @RegionId
                {filterCondition}
                {runwayCondition}
        ),
        combined_data AS (
            SELECT 
                COALESCE(ltd.LedgerId, cld.LedgerId) AS LedgerId,
                COALESCE(ltd.LedgerName, cld.LedgerName) AS LedgerName,
                COALESCE(ltd.Runway, cld.Runway) AS Runway,
                COALESCE(ltd.LedgerTypeName, cld.LedgerTypeName) AS LedgerTypeName
            FROM ledgerTypeData ltd
            FULL OUTER JOIN cityLedgerData cld ON ltd.LedgerId = cld.LedgerId
        )";

        // 构建数据查询SQL
        var sqlBuilder = new StringBuilder(baseSql);
        sqlBuilder.Append(@"
        SELECT 
            LedgerId,
            LedgerName,
            Runway,
            LedgerTypeName
        FROM combined_data
        ORDER BY LedgerId
        LIMIT @Take 
        OFFSET @Skip;
        ");

        // 构建计数查询SQL
        var sqlCountBuilder = new StringBuilder(baseSql);
        sqlCountBuilder.Append(@"
        SELECT COUNT(*) FROM combined_data;
        ");

        // 定义查询参数
        var parameters = new
        {
            Take = take,
            Skip = skip,
            Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null,
            RegionId = regionId,
            Runway = runway
        };

        // 串行执行查询，避免并发问题
        var totalCount = await connection.ExecuteScalarAsync<int>(sqlCountBuilder.ToString(), parameters);
        var result = (await connection.QueryAsync<LedgerDetailListView>(sqlBuilder.ToString(), parameters)).ToList();

        return (totalCount, result);
    }


    /// <summary>
    /// 区县运维员  各区级统计 - 详情（已优化SQL）
    /// </summary>
    /// <param name="take"></param>
    /// <param name="skip"></param>
    /// <param name="regionId"></param>
    /// <param name="filter"></param>
    /// <param name="ledgerTypeId"></param>
    /// <param name="runway"></param>
    /// <returns></returns>
    public override async Task<(int, List<LedgerDetailListView>)> GetCountiesLedgerDetailListByLedgerType(
        int take, int skip, Guid regionId, string filter, Guid ledgerTypeId, string runway)
    {
        var connection = await GetDbConnectionAsync();

        // 构建动态过滤条件
        var filterCondition = !string.IsNullOrEmpty(filter) ? " AND ll.\"Name\" LIKE @Filter" : "";
        var runwayCondition = !string.IsNullOrEmpty(runway) ? " AND ll.\"Runway\" = @Runway" : "";

        // 使用CTE优化查询
        var baseSql = $@"
        WITH filtered_ledgers AS (
            SELECT DISTINCT
                ll.`Id` AS LedgerId,
                ll.`Name` AS LedgerName,
                ll.`Runway` AS Runway,
                llt.`Name` AS LedgerTypeName
            FROM `Ledger_Ledgers` ll 
            INNER JOIN `Ledger_LedgerUsers` llu ON ll.`Id` = llu.`LedgerId` 
            INNER JOIN `Platform_Departments` pd2 ON llu.`DepartmentId` = pd2.`Id` 
            INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
            INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id` 
            INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id` 
            WHERE ll.`IsDeleted` = FALSE
                AND ll.`Name` NOT LIKE '%【测试】%'
                AND ll.`IsOnline` = TRUE 
                AND llt.`IsDeleted` = FALSE
                AND pd.`IsDeleted` = FALSE
                AND pr.`IsDeleted` = FALSE
                AND pd2.`IsDeleted` = FALSE
                AND pr.`Grade` = 3 
                AND pr.`Id` = @RegionId
                AND ll.`LedgerTypeId` = @LedgerTypeId
                {filterCondition}
                {runwayCondition}
        )";

        // 构建数据查询SQL
        var sqlBuilder = new StringBuilder(baseSql);
        sqlBuilder.Append(@"
        SELECT 
            LedgerId,
            LedgerName,
            Runway,
            LedgerTypeName
        FROM filtered_ledgers
        ORDER BY LedgerName
        LIMIT @Take 
        OFFSET @Skip;
        ");

        // 构建计数查询SQL
        var sqlCountBuilder = new StringBuilder(baseSql);
        sqlCountBuilder.Append(@"
        SELECT COUNT(*) FROM filtered_ledgers;
        ");

        // 定义查询参数
        var parameters = new
        {
            Take = take,
            Skip = skip,
            RegionId = regionId,
            Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null,
            LedgerTypeId = ledgerTypeId,
            Runway = runway
        };

        // 串行执行查询，避免并发问题
        var totalCount = await connection.ExecuteScalarAsync<int>(sqlCountBuilder.ToString(), parameters);
        var result = (await connection.QueryAsync<LedgerDetailListView>(sqlBuilder.ToString(), parameters)).ToList();

        return (totalCount, result);
    }


    /// <summary>
    /// 区县运维员  各市级统计 - 详情（已优化Sql）
    /// </summary>
    /// <param name="grade"></param>
    /// <param name="take"></param>
    /// <param name="skip"></param>
    /// <param name="regionId"></param>
    /// <param name="filter"></param>
    /// <param name="ledgerTypeId"></param>
    /// <param name="runway"></param>
    /// <returns></returns>
    public async Task<(int, List<LedgerDetailListView>)> GetCityLedgerDetailListByLedgerType(
        int take, int skip, Guid regionId, string filter, Guid ledgerTypeId, string runway)
    {
        var connection = await GetDbConnectionAsync();
        var sqlBuilder = new StringBuilder();

        sqlBuilder.Append(@"
        WITH allData AS 
        (
         SELECT  
         ll.`Id` AS LedgerId,
         ll.`Name` AS LedgerName,
         ll.`Runway` as Runway,
         llt.`Name` as LedgerTypeName,
         ll.`LedgerTypeId` as LedgerTypeId
         FROM `Ledger_Ledgers` ll 
	     INNER JOIN `Ledger_LedgerUsers` llu ON ll.`Id` = llu.`LedgerId` 
	     INNER JOIN `Platform_Departments` pd2 ON llu.`DepartmentId` = pd2.`Id` 
	     INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
	     INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id` 
	     INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id` 
	     WHERE ll.`IsDeleted` = false ");

        // 动态添加过滤条件
        if (!string.IsNullOrEmpty(filter))
        {
            sqlBuilder.AppendLine(" AND ll.\"Name\" LIKE @Filter");
        }

        if (!string.IsNullOrEmpty(runway))
        {
            sqlBuilder.Append(@" AND ll.`Runway` = @Runway");
        }

        sqlBuilder.Append(@"
	     AND ll.`IsDeleted` != true
	     AND llt.`IsDeleted` != true
	     AND pd.`IsDeleted` != true 
	     AND pr.`IsDeleted` != true
	     AND pd2.`IsDeleted` != true
	     AND pr.`Grade` = 3
         AND pr.`Id` = @RegionId
	     AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
         AND ll.`Name` NOT LIKE '%【测试】%'
	     AND ll.`IsOnline` = TRUE 
         AND ll.`LedgerTypeId` = @LedgerTypeId
        )
        SELECT ad.LedgerId, ad.LedgerName, ad.Runway, ad.LedgerTypeName,ad.LedgerTypeId
        FROM allData ad
	    INNER JOIN `Ledger_LedgerDepartments` lld ON ad.LedgerId = lld.`LedgerId`
        INNER JOIN `Platform_Departments` lpd ON lld.`AuthDepartmentId` = lpd.`Id`
        INNER JOIN `Platform_Regions` lpr ON lpd.`RegionId` = lpr.`Id`
        INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id`
        INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id`
        WHERE lpr.`Grade` = 2 AND apr.`Grade` = 3
        GROUP BY ad.LedgerId, ad.LedgerName
        LIMIT @Take 
        OFFSET @Skip;");

        var sql = sqlBuilder.ToString();


        var sqlCountBuilder = new StringBuilder();

        sqlCountBuilder.Append(@"
SELECT 
    COUNT(*)
FROM (
        WITH allData AS 
        (
         SELECT  
         ll.`Id` AS LedgerId,
         ll.`Name` AS LedgerName,
         ll.`Runway` as Runway,
         llt.`Name` as LedgerTypeName,
         ll.`LedgerTypeId` as LedgerTypeId
         FROM `Ledger_Ledgers` ll 
	     INNER JOIN `Ledger_LedgerUsers` llu ON ll.`Id` = llu.`LedgerId` 
	     INNER JOIN `Platform_Departments` pd2 ON llu.`DepartmentId` = pd2.`Id` 
	     INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
	     INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id` 
	     INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id` 
	     WHERE ll.`IsDeleted` = false ");

        // 动态添加过滤条件
        if (!string.IsNullOrEmpty(filter))
        {
            sqlCountBuilder.AppendLine(" AND ll.\"Name\" LIKE @Filter");
        }

        if (!string.IsNullOrEmpty(runway))
        {
            sqlCountBuilder.Append(@" AND ll.`Runway` = @Runway");
        }

        sqlCountBuilder.Append(@"
	     AND ll.`IsDeleted` != true
	     AND llt.`IsDeleted` != true
	     AND pd.`IsDeleted` != true 
	     AND pr.`IsDeleted` != true
	     AND pd2.`IsDeleted` != true
	     AND pr.`Grade` = 3
         AND pr.`Id` = @RegionId
	     AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
         AND ll.`Name` NOT LIKE '%【测试】%'
	     AND ll.`IsOnline` = TRUE 
         AND ll.`LedgerTypeId` = @LedgerTypeId
        )
        SELECT ad.LedgerId, ad.LedgerName, ad.Runway, ad.LedgerTypeName,ad.LedgerTypeId
        FROM allData ad
	    INNER JOIN `Ledger_LedgerDepartments` lld ON ad.LedgerId = lld.`LedgerId`
        INNER JOIN `Platform_Departments` lpd ON lld.`AuthDepartmentId` = lpd.`Id`
        INNER JOIN `Platform_Regions` lpr ON lpd.`RegionId` = lpr.`Id`
        INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id`
        INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id`
        WHERE lpr.`Grade` = 2 AND apr.`Grade` = 3
        GROUP BY ad.LedgerId, ad.LedgerName
);");


        var parameters = new
        {
            //Grade = grade,
            Take = take,
            Skip = skip,
            RegionId = regionId,
            Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null,
            LedgerTypeId = ledgerTypeId,
            Runway = runway
        };

        var totalCount = await connection.ExecuteScalarAsync<int>(sqlCountBuilder.ToString(), parameters);
        var result = (await connection.QueryAsync<LedgerDetailListView>(sql, parameters)).ToList();
        return (totalCount, result);
    }


    /// <summary>
    /// 管理员/区县运维员 - 单个 区县/街镇 任务统计（已优化sql）
    /// </summary>
    /// <param name="regionId"></param>
    /// <param name="streetId"></param>
    /// <returns></returns>
    public override async Task<TelecomTaskStatisticsView> GetTelecomTaskStatisticsByRegionAsync(Guid regionId,
        Guid? streetId)
    {
        var connection = await GetDbConnectionAsync();
        var sqlBuilder = new StringBuilder();

        sqlBuilder.Append(@"WITH base_data AS (
                SELECT 
                    pr.`Id` as `GridId`,
                    pr.`Name` as `GridName`,
                    pr2.`Id` as `RegionId`,
                    pr2.`Name` as `RegionName`,
                    ltti.`Id` as `TaskId`,
                    ltti.`TaskName`,
                    ltti.`CompleteStatus`,
                    CASE 
                        WHEN ltti.`CompleteStatus` = '1' THEN 1 
                        ELSE 0 
                    END as `IsFinished`,
                    CASE 
                        WHEN (ltti.`CompleteStatus` = '0' OR ltti.`CompleteStatus` IS NULL) THEN 1 
                        ELSE 0 
                    END as `IsUnfinished`,
                    pr.`Id` as `StreetId`,
                    pr.`Name` as `StreetName`
                FROM `Ledger_TelecomTaskItems` ltti 
                left JOIN `Platform_Regions` pr ON ltti.`GridCode` = pr.`TelecomCode` 
                left JOIN `Platform_Regions` pr2 ON pr.`ParentId` = pr2.`Id`
                left join `Ledger_LedgerRunways` as ra1 on ltti.`BusinessId` = ra1.`Id`
                left join `Ledger_LedgerRunways` as ra2 on ra1.`ParentId` = ra2.`Id`
                WHERE ltti.`PushType` = 0 AND ltti.`Status` = 2
                and ltti.`IsDeleted` != true
                AND (ltti.`ParentId` is null or ltti.`ParentId` = '')
                AND pr2.`Id` = @RegionId");

        if (streetId != null)
        {
            sqlBuilder.Append(@" AND pr.`Id` = @StreetId");
        }

        sqlBuilder.Append(@"
            )
            SELECT 
                @RegionId as `Id`,
                MAX(bd.`RegionName`) as `Name`");

        if (streetId != null)
        {
            sqlBuilder.Append(@",
                bd.`StreetId`,
                bd.`StreetName`");
        }

        sqlBuilder.Append(@",
                SUM(bd.`IsFinished`) as `FinishedCount`,
                COUNT(DISTINCT bd.`TaskId`) - SUM(bd.`IsFinished`) as `UnfinishedCount`,
                COUNT(DISTINCT bd.`TaskId`) as `AssociatedCount`,
                (
                    SELECT COUNT(DISTINCT ltti.`Id`) 
                    FROM `Ledger_TelecomTaskItems` ltti 
                    left JOIN `Platform_Regions` pr ON ltti.`GridCode` = pr.`TelecomCode` 
                    left JOIN `Platform_Regions` pr2 ON pr.`ParentId` = pr2.`Id`
                    left join `Ledger_LedgerRunways` as ra1 on ltti.`BusinessId` = ra1.`Id`
                	left join `Ledger_LedgerRunways` as ra2 on ra1.`ParentId` = ra2.`Id`
                    WHERE ltti.`PushType` = 0 
                    and ltti.`IsDeleted` != true
                    AND (ltti.`ParentId` is null or ltti.`ParentId` = '')
                    AND pr2.`Id` = @RegionId");

        if (streetId != null)
        {
            sqlBuilder.Append(@" AND pr.`Id` = @StreetId");
        }

        sqlBuilder.Append(@"
                ) as `TotalCount`
            FROM 
                base_data bd");

        // 根据 streetId 是否为 null 决定分组方式
        var groupByClause = streetId != null
            ? @"GROUP BY `Id`, bd.`StreetId`, bd.`StreetName`"
            : @"GROUP BY `Id`";

        sqlBuilder.Append($@"
            {groupByClause}
            ORDER BY 
                SUM(bd.`IsFinished`) + SUM(bd.`IsUnfinished`) DESC");

        var parameters = new
        {
            RegionId = regionId,
            StreetId = streetId
        };

        return await connection.QueryFirstOrDefaultAsync<TelecomTaskStatisticsView>(sqlBuilder.ToString(), parameters);
    }


    /// <summary>
    /// 管理员/区县运维员 - 单个 区县/镇街 任务列表
    /// </summary>
    /// <param name="regionId"></param>
    /// <param name="streetId"></param>
    /// <param name="taskName"></param>
    /// <param name="take"></param>
    /// <param name="skip"></param>
    /// <returns></returns>
    public override async Task<(int, List<TelecomTaskListView>)> GetTelecomTaskListAsync(Guid? regionId, Guid? streetId,
        string taskName, int take, int skip)
    {
        var connection = await GetDbConnectionAsync();

        // 构建基础查询条件，确保计数查询和数据查询使用相同的条件
        StringBuilder baseCondition = new StringBuilder();
        baseCondition.Append(
            " WHERE ltti.\"PushType\" = 0  AND (ltti.\"ParentId\" is null or ltti.\"ParentId\" = '') and ltti.\"IsDeleted\" != true");

        if (regionId.HasValue)
        {
            baseCondition.Append(" AND pr2.\"Id\" = @RegionId");
        }

        if (streetId.HasValue)
        {
            baseCondition.Append(" AND pr.\"Id\" = @StreetId");
        }

        if (!string.IsNullOrEmpty(taskName))
        {
            baseCondition.Append(" AND ltti.\"TaskName\" LIKE @TaskName");
        }

        // 构建数据查询
        StringBuilder dataQuery = new StringBuilder();
        dataQuery.Append(@"
            SELECT distinct ltti.`Id`, ltti.`TaskName`, ltti.`Leaders`, 
                   ltcp.`ChargePersonName`, ltti.`BusinessName`, 
                   ltti.`StartTime`, ltti.`EndTime` ,ltti.`Progress`,ltti.`CreationTime`
            FROM `Ledger_TelecomTaskItems` ltti 
            left JOIN `Platform_Regions` pr ON ltti.`GridCode` = pr.`TelecomCode`
            left JOIN `Platform_Regions` pr2 ON pr.`ParentId` = pr2.`Id`
            left JOIN `Ledger_TaskChargePeople` ltcp ON ltti.`Id` = ltcp.`TaskId`
            left join `Ledger_LedgerRunways` as ra1 on ltti.`BusinessId` = ra1.`Id`
            left join `Ledger_LedgerRunways` as ra2 on ra1.`ParentId` = ra2.`Id`
        ");

        dataQuery.Append(baseCondition);
        dataQuery.AppendLine(" ORDER BY ltti.\"CreationTime\" DESC");

        if (take > 0)
        {
            dataQuery.AppendLine("LIMIT @Skip, @Take");
        }

        // 构建计数查询，确保与数据查询使用相同的条件
        StringBuilder countQuery = new StringBuilder();
        countQuery.Append(@"
            SELECT COUNT(distinct ltti.`Id`) 
            FROM `Ledger_TelecomTaskItems` ltti 
            left JOIN `Platform_Regions` pr ON ltti.`GridCode` = pr.`TelecomCode` 
            left JOIN `Platform_Regions` pr2 ON pr.`ParentId` = pr2.`Id`
            left JOIN `Ledger_TaskChargePeople` ltcp ON ltti.`Id` = ltcp.`TaskId`
            left join `Ledger_LedgerRunways` as ra1 on ltti.`BusinessId` = ra1.`Id`
            left join `Ledger_LedgerRunways` as ra2 on ra1.`ParentId` = ra2.`Id`
        ");

        countQuery.Append(baseCondition);

        // 设置参数
        var parameters = new DynamicParameters();

        if (regionId.HasValue)
        {
            parameters.Add("@RegionId", regionId);
        }

        if (streetId.HasValue)
        {
            parameters.Add("@StreetId", streetId.Value);
        }

        if (!string.IsNullOrEmpty(taskName))
        {
            parameters.Add("@TaskName", $"%{taskName}%");
        }

        parameters.Add("@Take", take);
        parameters.Add("@Skip", skip);

        // 执行查询
        var totalCount = await connection.ExecuteScalarAsync<int>(countQuery.ToString(), parameters);
        var result = (await connection.QueryAsync<TelecomTaskListView>(dataQuery.ToString(), parameters)).ToList();

        return (totalCount, result);
    }


    /// <summary>
    /// 任务详情/子任务详情
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    public override async Task<TelecomTaskItemView> GetTelecomTaskItemAsync(string taskId)
    {
        var connection = await GetDbConnectionAsync();
        StringBuilder sbuilder = new StringBuilder();

        sbuilder.Append(@"
   WITH subtask_count AS (
    SELECT `ParentId`, COUNT(*) as subtask_count
    FROM `Ledger_TelecomTaskItems`
    WHERE `ParentId` = @TaskId
    GROUP BY `ParentId`
)
SELECT 
    ltti.`Id`,
    ltti.`TaskName`,
    ltti.`BusinessIdPath`,
    ltti.`Leaders`,
    ltcp.`ChargePersonName`,
    ltti.`BusinessName`,
    ltti.`StartTime`,
    ltti.`EndTime`,
    ltti.`Progress`,ltti.`Status`,
    ll.`Name`,
    COALESCE(sc.subtask_count, 0) as `SubtaskNum`,
    ltti.`TaskCompleteCon`,
    ltti.`TaskContent`
FROM `Ledger_TelecomTaskItems` ltti 
LEFT JOIN `Platform_Regions` pr ON ltti.`GridCode` = pr.`TelecomCode` 
LEFT JOIN `Platform_Regions` pr2 ON pr.`ParentId` = pr2.`Id`
LEFT JOIN `Ledger_TaskChargePeople` ltcp ON ltti.`Id` = ltcp.`TaskId` 
LEFT JOIN `Ledger_LedgerRunways` as ra1 ON ltti.`BusinessId` = ra1.`Id`
LEFT JOIN `Ledger_LedgerRunways` as ra2 ON ra1.`ParentId` = ra2.`Id`
LEFT JOIN `Ledger_Ledgers` ll ON ltti.`LedgerId` = ll.`Id` 
LEFT JOIN subtask_count sc ON ltti.`Id` = sc.`ParentId`
WHERE ltti.`Id` = @TaskId
    ");

        sbuilder.Append(" AND ltti.\"Id\" = @TaskId");

        var parameters = new DynamicParameters();
        parameters.Add("@TaskId", taskId);

        return await connection.QueryFirstOrDefaultAsync<TelecomTaskItemView>(sbuilder.ToString(), parameters);
    }


    /// <summary>
    /// 管理员 区县台账列表（分接收市级和区级发布）
    /// </summary>
    /// <param name="bigDepartmentId"></param>
    /// <param name="pageSize"></param>
    /// <param name="pageIndex"></param>
    /// <param name="regionId"></param>
    /// <param name="userId"></param>
    /// <param name="runway"></param>
    /// <param name="ledgerTypeId"></param>
    /// <param name="name"></param>
    /// <param name="departmentId"></param>
    /// <param name="reminderInterval"></param>
    /// <param name="autoFillStatus"></param>
    /// <param name="litStatus"></param>
    /// <returns></returns>
    public override async Task<(int, List<LedgerDetailListView>)> GetLedgerListByAdminAsync(Guid? bigDepartmentId,
        int pageSize, int pageIndex, Guid regionId, Guid userId, string runway, Guid? ledgerTypeId,
        string name, Guid? departmentId, ReminderInterval? reminderInterval, MyLedgerDtoAutoFillStatus? autoFillStatus,
        MyLedgerDtoLitStatus? litStatus)
    {
        var connection = await GetDbConnectionAsync();
        var sqlBuilder = new StringBuilder();

        sqlBuilder.Append(@"
    WITH ledgerTypeData AS (
        SELECT 
            ll.`Id` AS LedgerId,
            ll.`Name` AS LedgerName,
            llt.`Name` AS LedgerTypeName,
            COALESCE(lld.`LedgerDataLastUpdatedTime`, NULL) as `LedgerDataLastUpdatedTime`,
            ll.`ReminderInterval`,
            ll.`Runway`,
                @UserId as UserId,
                llu.`Permissions`,
            lld.`LitStatus`
        FROM `Ledger_Ledgers` ll 
        LEFT JOIN `Ledger_LedgerUsers` llu ON ll.`Id` = llu.`LedgerId` 
            AND llu.`UserId` = @UserId
        INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
        INNER JOIN `Platform_Departments` pd ON llt.`BindDepartmentId` = pd.`Id`
        INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id`
        LEFT JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId`
        WHERE 
            pr.`Grade` = 3
            AND ll.`IsOnline` = true 
            AND ll.`IsDeleted` = false 
            AND pd.`IsDeleted` = false
            AND pr.`IsDeleted` = false
            AND llt.`IsDeleted` = false
            AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
            AND ll.`Name` NOT LIKE '%【测试】%'");

        if (!string.IsNullOrEmpty(runway))
        {
            sqlBuilder.Append(@" AND ll.`Runway` = @Runway");
        }

        // if (ledgerTypeId.HasValue)
        // {
        //     sqlBuilder.Append(@" AND ll.`LedgerTypeId` = @LedgerTypeId");
        // }

        if (!string.IsNullOrEmpty(name))
        {
            sqlBuilder.Append(@" AND ll.`Name` LIKE @Name");
        }

        if (departmentId.HasValue)
        {
            sqlBuilder.Append(@" AND llt.`Id` = @DepartmentId");
        }

        if (reminderInterval.HasValue)
        {
            sqlBuilder.Append(@" AND ll.`ReminderInterval` = @ReminderInterval");
        }

        if (autoFillStatus.HasValue)
        {
            switch (autoFillStatus)
            {
                case MyLedgerDtoAutoFillStatus.NotNeedFill:
                    sqlBuilder.Append(@" AND (NOT (llu.`Permissions` && ARRAY[1,2,3]))");
                    break;
                case MyLedgerDtoAutoFillStatus.NeedToFilled:
                    sqlBuilder.Append(@" AND (
		                                    llu.`Permissions` IS NOT NULL 
		                                    AND (llu.`Permissions` && ARRAY[1,2,3])
		                                    AND lld.`DepartmentId` = @DepartmentId
		                                    AND lld.`LitStatus` = 0
		                                    AND ll.`ReminderInterval` != 0
		                                )");
                    break;
                case MyLedgerDtoAutoFillStatus.NeedToNotFill:
                    sqlBuilder.Append(@" AND (
		                                    llu.`Permissions` IS NOT NULL 
		                                    AND (llu.`Permissions` && ARRAY[1,2,3])
		                                    AND lld.`DepartmentId` = @DepartmentId
		                                    AND lld.`LitStatus` != 0
		                                    AND ll.`ReminderInterval` != 0
		                                )");
                    break;
                case MyLedgerDtoAutoFillStatus.OtherFill:
                    sqlBuilder.Append(@" AND (
		                                    llu.`Permissions` IS NOT NULL 
		                                    AND (llu.`Permissions` && ARRAY[1,2,3])
		                                    AND ll.`ReminderInterval` = 0
		                                )");
                    break;
            }
        }

        if (litStatus.HasValue)
        {
            sqlBuilder.Append(@" AND lld.`LitStatus` = @LitStatus");
        }

        sqlBuilder.Append(@" AND pr.`Id` = @RegionId
        GROUP BY ll.`Id`
        order by ll.`CreationTime` desc
    ),
    cityLedgerData AS (
        SELECT 
            ll.`Id` AS LedgerId,
            ll.`Name` AS LedgerName,
            llt.`Name` AS LedgerTypeName,
            COALESCE(lld.`LedgerDataLastUpdatedTime`, NULL) as `LedgerDataLastUpdatedTime`,
            ll.`ReminderInterval`,
            ll.`Runway`,
            @UserId as UserId,
            llu.`Permissions`,
            lld.`LitStatus`
        FROM `Ledger_Ledgers` ll 
        LEFT JOIN `Ledger_LedgerUsers` llu ON ll.`Id` = llu.`LedgerId` 
            AND llu.`UserId` = @UserId
        INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
	    INNER JOIN `Platform_Departments` pd ON llt.`BindDepartmentId` = pd.`Id`
	    INNER JOIN `Platform_Regions` pr3 ON pd.`RegionId` = pr3.`Id`
	    INNER JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId`
	    INNER JOIN `Platform_Departments` lpd ON lld.`AuthDepartmentId` = lpd.`Id`
	    INNER JOIN `Platform_Regions` lpr ON lpd.`RegionId` = lpr.`Id`
	    INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id`
	    INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id`
	    WHERE 
	        lpr.`Grade` = 2
	        AND apr.`Grade` = 3
	        AND pr3.`Grade` = 2
	        AND ll.`IsOnline` = true 
	        AND ll.`IsDeleted` = false 
	        AND lpr.`IsDeleted` = false
	        AND apr.`IsDeleted` = false
	        AND apd.`IsDeleted` = false
	        AND lpd.`IsDeleted` = false
	        AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
	        AND ll.`Name` NOT LIKE '%【测试】%'");

        if (!string.IsNullOrEmpty(runway))
        {
            sqlBuilder.Append(@" AND ll.`Runway` = @Runway");
        }

        // if (ledgerTypeId.HasValue)
        // {
        //     sqlBuilder.Append(@" AND ll.`LedgerTypeId` = @LedgerTypeId");
        // }

        if (!string.IsNullOrEmpty(name))
        {
            sqlBuilder.Append(@" AND ll.`Name` LIKE @Name");
        }

        if (departmentId.HasValue)
        {
            sqlBuilder.Append(@" AND llt.`Id` = @DepartmentId");
        }

        if (reminderInterval.HasValue)
        {
            sqlBuilder.Append(@" AND ll.`ReminderInterval` = @ReminderInterval");
        }

        if (autoFillStatus.HasValue)
        {
            switch (autoFillStatus)
            {
                case MyLedgerDtoAutoFillStatus.NotNeedFill:
                    sqlBuilder.Append(@" AND (NOT (llu.`Permissions` && ARRAY[1,2,3]))");
                    break;
                case MyLedgerDtoAutoFillStatus.NeedToFilled:
                    sqlBuilder.Append(@" AND (
		                                    llu.`Permissions` IS NOT NULL 
		                                    AND (llu.`Permissions` && ARRAY[1,2,3])
		                                    AND lld.`DepartmentId` = @BigDepartmentId
		                                    AND lld.`LitStatus` = 0
		                                    AND ll.`ReminderInterval` != 0
		                                )");
                    break;
                case MyLedgerDtoAutoFillStatus.NeedToNotFill:
                    sqlBuilder.Append(@" AND (
		                                    llu.`Permissions` IS NOT NULL 
		                                    AND (llu.`Permissions` && ARRAY[1,2,3])
		                                    AND lld.`DepartmentId` = @BigDepartmentId
		                                    AND lld.`LitStatus` != 0
		                                    AND ll.`ReminderInterval` != 0
		                                )");
                    break;
                case MyLedgerDtoAutoFillStatus.OtherFill:
                    sqlBuilder.Append(@" AND (
		                                    llu.`Permissions` IS NOT NULL 
		                                    AND (llu.`Permissions` && ARRAY[1,2,3])
		                                    AND ll.`ReminderInterval` = 0
		                                )");
                    break;
            }
        }

        if (litStatus.HasValue)
        {
            sqlBuilder.Append(@" AND lld.`LitStatus` = @LitStatus");
        }

        sqlBuilder.Append(@" AND apr.`Id` = @RegionId
        GROUP BY ll.`Id`
        order by ll.`CreationTime` desc
    )
    SELECT * 
    FROM (
        SELECT * FROM ledgerTypeData
        UNION ALL
        SELECT * FROM cityLedgerData
    ) AS CombinedData
    LIMIT @PageSize OFFSET @Skip;");

        var sqlCountBuilder = new StringBuilder();
        sqlCountBuilder.Append(sqlBuilder.ToString().Replace(
            @"SELECT * 
    FROM (
        SELECT * FROM ledgerTypeData
        UNION ALL
        SELECT * FROM cityLedgerData
    ) AS CombinedData
    LIMIT @PageSize OFFSET @Skip;",
            @"SELECT COUNT(*) 
    FROM (
        SELECT * FROM ledgerTypeData
        UNION ALL
        SELECT * FROM cityLedgerData
    ) AS CombinedData;"));

        var parameters = new
        {
            RegionId = regionId,
            PageSize = pageSize,
            Skip = pageIndex,
            Runway = runway,
            LedgerTypeId = ledgerTypeId,
            UserId = userId,
            Name = !string.IsNullOrEmpty(name) ? $"%{name}%" : null,
            DepartmentId = departmentId,
            ReminderInterval = reminderInterval,
            AutoFillStatus = autoFillStatus,
            LitStatus = litStatus,
            BigDepartmentId = bigDepartmentId
        };

        var totalCount = await connection.ExecuteScalarAsync<int>(sqlCountBuilder.ToString(), parameters);
        var result = (await connection.QueryAsync<LedgerDetailListView>(sqlBuilder.ToString(), parameters)).ToList();

        return (totalCount, result);
    }

    /// <summary>
    /// 管理员 市级台账列表
    /// </summary>
    /// <param name="bigDepartmentId"></param>
    /// <param name="pageSize"></param>
    /// <param name="pageIndex"></param>
    /// <param name="ledgerTypeId"></param>
    /// <param name="runway"></param>
    /// <param name="userId"></param>
    /// <param name="name"></param>
    /// <param name="departmentId"></param>
    /// <param name="reminderInterval"></param>
    /// <param name="autoFillStatus"></param>
    /// <param name="litStatus"></param>
    /// <returns></returns>
    public override async Task<(int, List<LedgerDetailListView>)> GetCityLedgerListByAdminAsync(Guid? bigDepartmentId,
        int pageSize, int pageIndex, Guid? ledgerTypeId, string runway, Guid? userId, string name, Guid? departmentId,
        ReminderInterval? reminderInterval, MyLedgerDtoAutoFillStatus? autoFillStatus,
        MyLedgerDtoLitStatus? litStatus)
    {
        var parameters = new
        {
            PageSize = pageSize,
            Offset = pageIndex,
            LedgerTypeId = ledgerTypeId,
            Runway = runway,
            UserId = userId,
            Name = !string.IsNullOrEmpty(name) ? $"%{name}%" : null,
            DepartmentId = departmentId,
            ReminderInterval = reminderInterval,
            AutoFillStatus = autoFillStatus,
            LitStatus = litStatus,
            BigDepartmentId = bigDepartmentId
        };

        var baseCte = @"WITH filtered_ledgers AS (
            SELECT 
                ll.`Id` AS LedgerId,
                ll.`Name` AS LedgerName,
                llt.`Name` AS LedgerTypeName,
                COALESCE(lld.`LedgerDataLastUpdatedTime`, NULL) as `LedgerDataLastUpdatedTime`,
                ll.`ReminderInterval`,
                ll.`Runway`,
                @UserId as UserId,
                llu.`Permissions`,
                lld.`LitStatus`
            FROM `Ledger_Ledgers` ll 
            LEFT JOIN `Ledger_LedgerUsers` llu ON ll.`Id` = llu.`LedgerId` 
                AND llu.`UserId` = @UserId
            INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
            INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
            INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id`
            LEFT JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId`
            WHERE 
                pr.`Grade` = 2
                AND ll.`IsOnline` = true 
                AND ll.`IsDeleted` = false 
                AND pd.`IsDeleted` = false
                AND pr.`IsDeleted` = false
                AND llt.`IsDeleted` = false
                AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
                AND ll.`Name` NOT LIKE '%【测试】%'
                AND ll.`LedgerTypeId` = @LedgerTypeId";

        var conditions = new StringBuilder();
        if (!string.IsNullOrEmpty(name))
        {
            conditions.AppendLine("AND ll.\"Name\" LIKE @Name");
        }

        if (!string.IsNullOrEmpty(runway))
        {
            conditions.AppendLine("AND ll.\"Runway\" = @Runway");
        }

        if (departmentId.HasValue)
        {
            conditions.Append(@" AND llt.`Id` = @DepartmentId ");
        }

        if (reminderInterval.HasValue)
        {
            conditions.Append(@" AND ll.`ReminderInterval` = @ReminderInterval ");
        }

        if (autoFillStatus.HasValue)
        {
            switch (autoFillStatus)
            {
                case MyLedgerDtoAutoFillStatus.NotNeedFill:
                    conditions.Append(@" AND (NOT (llu.`Permissions` && ARRAY[1,2,3])) ");
                    break;
                case MyLedgerDtoAutoFillStatus.NeedToFilled:
                    conditions.Append(@" AND (
		                                    llu.`Permissions` IS NOT NULL 
		                                    AND (llu.`Permissions` && ARRAY[1,2,3])
		                                    AND lld.`DepartmentId` = @BigDepartmentId
		                                    AND lld.`LitStatus` = 0
		                                    AND ll.`ReminderInterval` != 0
		                                ) ");
                    break;
                case MyLedgerDtoAutoFillStatus.NeedToNotFill:
                    conditions.Append(@" AND (
		                                    llu.`Permissions` IS NOT NULL 
		                                    AND (llu.`Permissions` && ARRAY[1,2,3])
		                                    AND lld.`DepartmentId` = @BigDepartmentId
		                                    AND lld.`LitStatus` != 0
		                                    AND ll.`ReminderInterval` != 0
		                                ) ");
                    break;
                case MyLedgerDtoAutoFillStatus.OtherFill:
                    conditions.Append(@" AND (
		                                    llu.`Permissions` IS NOT NULL 
		                                    AND (llu.`Permissions` && ARRAY[1,2,3])
		                                    AND ll.`ReminderInterval` = 0
		                                ) ");
                    break;
            }
        }

        if (litStatus.HasValue)
        {
            conditions.Append(@" AND lld.`LitStatus` = @LitStatus ");
        }

        conditions.AppendLine(" GROUP BY ll.\"Id\"");

        // 获取总数的SQL
        var countSql = new StringBuilder();
        countSql.AppendLine(baseCte);
        countSql.Append(conditions);
        countSql.AppendLine(")");
        countSql.AppendLine("SELECT COUNT(DISTINCT LedgerId) FROM filtered_ledgers");

        // 获取分页数据的SQL
        var sql = new StringBuilder();
        sql.AppendLine(baseCte);
        sql.Append(conditions);
        sql.AppendLine(")");
        sql.AppendLine(@"SELECT 
            LedgerId,
            LedgerName,
            LedgerTypeName,
            `LedgerDataLastUpdatedTime`,
            `ReminderInterval`,
            `Runway`,
            UserId,
            `Permissions`,
             `LitStatus`
        FROM filtered_ledgers
        ORDER BY LedgerId DESC
        LIMIT @PageSize OFFSET @Offset");

        // 获取数据库连接
        var connection = await GetDbConnectionAsync();

        // 执行查询
        var totalCount = await connection.ExecuteScalarAsync<int>(countSql.ToString(), parameters);
        var result = (await connection.QueryAsync<LedgerDetailListView>(sql.ToString(), parameters)).ToList();

        return (totalCount, result);
    }

    /// <summary>
    /// 区县运维员 - 区级业务表列表
    /// </summary>
    /// <param name="bigDepartmentId"></param>
    /// <param name="pageSize"></param>
    /// <param name="pageIndex"></param>
    /// <param name="regionId"></param>
    /// <param name="userId"></param>
    /// <param name="runway"></param>
    /// <param name="ledgerTypeId"></param>
    /// <param name="name"></param>
    /// <param name="departmentId"></param>
    /// <param name="reminderInterval"></param>
    /// <param name="autoFillStatus"></param>
    /// <param name="litStatus"></param>
    /// <returns></returns>
    public override async Task<(int, List<LedgerDetailListView>)> GetLedgerListByCountiesAsync(Guid? bigDepartmentId,
        int pageSize, int pageIndex, Guid regionId, Guid userId, string runway, Guid? ledgerTypeId,
        string name, Guid? departmentId,
        ReminderInterval? reminderInterval, MyLedgerDtoAutoFillStatus? autoFillStatus,
        MyLedgerDtoLitStatus? litStatus)
    {
        var parameters = new
        {
            RegionId = regionId,
            UserId = userId,
            PageSize = pageSize,
            Offset = pageIndex,
            Runway = runway,
            LedgerTypeId = ledgerTypeId,
            Name = !string.IsNullOrEmpty(name) ? $"%{name}%" : null,
            DepartmentId = departmentId,
            ReminderInterval = reminderInterval,
            AutoFillStatus = autoFillStatus,
            LitStatus = litStatus,
            BigDepartmentId = bigDepartmentId
        };

        var sql = new StringBuilder();
        sql.AppendLine(@"WITH filtered_ledgers AS (
        WITH RankedLedgers AS (
            SELECT 
                ll.`Id` AS LedgerId,
                ll.`Name` AS LedgerName,
                llt.`Name` AS LedgerTypeName,
                llu.`Permissions`,
                COALESCE(lld.`LedgerDataLastUpdatedTime`, NULL) as `LedgerDataLastUpdatedTime`,
                ll.`ReminderInterval`,
                ll.`Runway`,
                ROW_NUMBER() OVER (PARTITION BY ll.`Id` ORDER BY lld.`LedgerDataLastUpdatedTime`) as rn,
                lld.`LitStatus`
            FROM `Ledger_Ledgers` ll 
            LEFT JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
            LEFT JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId`
            LEFT JOIN `Ledger_LedgerUsers` llu ON ll.`Id` = llu.`LedgerId` AND llu.`UserId` = @UserId
            INNER JOIN `Platform_Departments` pd2 ON llt.`DepartmentId` = pd2.`Id`
            INNER JOIN `Platform_Regions` pr ON pd2.`RegionId` = pr.`Id`
            WHERE ll.`IsDeleted` = false
            AND llt.`IsDeleted` != true
            AND pd2.`IsDeleted` != true
            AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
            AND ll.`Name` NOT LIKE '%【测试】%'
            AND ll.`IsOnline` = true
            AND pr.`Id` = @RegionId");

        if (ledgerTypeId.HasValue)
        {
            sql.AppendLine("AND llt.\"Id\" = @LedgerTypeId");
        }

        if (!string.IsNullOrEmpty(name))
        {
            sql.AppendLine("AND ll.\"Name\" LIKE @Name");
        }

        if (!string.IsNullOrEmpty(runway))
        {
            sql.AppendLine("AND ll.\"Runway\" = @Runway");
        }


        if (departmentId.HasValue)
        {
            sql.AppendLine(@" AND llt.`DepartmentId` = @DepartmentId ");
        }

        if (reminderInterval.HasValue)
        {
            sql.AppendLine(@" AND ll.`ReminderInterval` = @ReminderInterval ");
        }

        if (autoFillStatus.HasValue)
        {
            switch (autoFillStatus)
            {
                case MyLedgerDtoAutoFillStatus.NotNeedFill:
                    sql.AppendLine(@" AND (llu.`Permissions` IS NULL OR NOT (llu.`Permissions` && ARRAY[1,2,3])) ");
                    break;
                case MyLedgerDtoAutoFillStatus.NeedToFilled:
                    sql.AppendLine(@" AND (
                                        llu.`Permissions` IS NOT NULL 
                                        AND (llu.`Permissions` && ARRAY[1,2,3])
                                        AND lld.`DepartmentId` = @BigDepartmentId
                                        AND lld.`LitStatus` = 0
                                        AND ll.`ReminderInterval` != 0
                                    ) ");
                    break;
                case MyLedgerDtoAutoFillStatus.NeedToNotFill:
                    sql.AppendLine(@" AND (
                                        llu.`Permissions` IS NOT NULL 
                                        AND (llu.`Permissions` && ARRAY[1,2,3])
                                        AND lld.`DepartmentId` = @BigDepartmentId
                                        AND lld.`LitStatus` != 0
                                        AND ll.`ReminderInterval` != 0
                                    ) ");
                    break;
                case MyLedgerDtoAutoFillStatus.OtherFill:
                    sql.AppendLine(@" AND (
                                        llu.`Permissions` IS NOT NULL 
                                        AND (llu.`Permissions` && ARRAY[1,2,3])
                                        AND ll.`ReminderInterval` = 0
                                    ) ");
                    break;
            }
        }

        if (litStatus.HasValue)
        {
            sql.AppendLine(@" AND lld.`LitStatus` = @LitStatus ");
        }

        sql.AppendLine(@")
        SELECT 
            LedgerId,
            LedgerName,
            LedgerTypeName,
            `Permissions`,
            `LedgerDataLastUpdatedTime`,
            `ReminderInterval`,
            `Runway`,
            `LitStatus`
        FROM RankedLedgers
        WHERE rn = 1
        ORDER BY `LedgerDataLastUpdatedTime`
    ),
    valid_departments AS (
        SELECT DISTINCT 
            lld.`LedgerId`
        FROM `Ledger_LedgerDepartments` lld
        INNER JOIN `Platform_Departments` lpd ON lld.`AuthDepartmentId` = lpd.`Id`
        INNER JOIN `Platform_Regions` lpr ON lpd.`RegionId` = lpr.`Id`
        INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id`
        INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id`
        WHERE apr.`Grade` = 2
    )");

        // 获取总数的SQL
        var countSql = new StringBuilder();
        countSql.Append(sql);
        countSql.AppendLine(@"SELECT COUNT(DISTINCT fl.LedgerId) 
        FROM filtered_ledgers fl
        LEFT JOIN valid_departments vd ON fl.LedgerId = vd.`LedgerId`");

        // 添加分页查询
        sql.AppendLine(@"SELECT DISTINCT
        fl.LedgerId,
        fl.LedgerName,
        fl.LedgerTypeName,
        fl.`Permissions`,
        fl.`LedgerDataLastUpdatedTime`,
        fl.`ReminderInterval`,
        fl.`Runway`,
        fl.`LitStatus`
    FROM filtered_ledgers fl
    LEFT JOIN valid_departments vd ON fl.LedgerId = vd.`LedgerId`
    ORDER BY fl.LedgerId DESC
    LIMIT @PageSize OFFSET @Offset");

        // 获取数据库连接
        var connection = await GetDbConnectionAsync();

        // 执行查询
        var totalCount = await connection.ExecuteScalarAsync<int>(countSql.ToString(), parameters);
        var result = (await connection.QueryAsync<LedgerDetailListView>(sql.ToString(), parameters)).ToList();

        return (totalCount, result);
    }

    /// <summary>
    /// 区县运维员 - 市级台账列表
    /// </summary>
    /// <param name="bigDepartmentId"></param>
    /// <param name="pageSize"></param>
    /// <param name="pageIndex"></param>
    /// <param name="regionId"></param>
    /// <param name="userId"></param>
    /// <param name="runway"></param>
    /// <param name="ledgerTypeId"></param>
    /// <param name="name"></param>
    /// <param name="departmentId"></param>
    /// <param name="reminderInterval"></param>
    /// <param name="autoFillStatus"></param>
    /// <param name="litStatus"></param>
    /// <returns></returns>
    public override async Task<(int, List<LedgerDetailListView>)> GetCityLedgerListByCountiesAsync(Guid? bigDepartmentId,
        int pageSize, int pageIndex, Guid regionId, Guid userId, string runway, Guid? ledgerTypeId,
        string name, Guid? departmentId,
        ReminderInterval? reminderInterval, MyLedgerDtoAutoFillStatus? autoFillStatus,
        MyLedgerDtoLitStatus? litStatus)
    {
        var parameters = new
        {
            RegionId = regionId,
            PageSize = pageSize,
            Offset = pageIndex,
            Runway = runway,
            LedgerTypeId = ledgerTypeId,
            UserId = userId,
            Name = !string.IsNullOrEmpty(name) ? $"%{name}%" : null,
            DepartmentId = departmentId,
            ReminderInterval = reminderInterval,
            AutoFillStatus = autoFillStatus,
            LitStatus = litStatus,
            BigDepartmentId = bigDepartmentId
        };

        var baseCte = @"WITH filtered_ledgers AS (
            SELECT DISTINCT
                ll.`Id` AS LedgerId,
                ll.`Name` AS LedgerName,
                llt.`Name` AS LedgerTypeName,
                COALESCE(NULL) as `LedgerDataLastUpdatedTime`,
                ll.`ReminderInterval`,
                ll.`Runway`,
                @UserId as UserId,
                llu.`Permissions`,
                lld.`LitStatus`
            FROM `Ledger_Ledgers` ll 
            LEFT JOIN `Ledger_LedgerUsers` llu ON ll.`Id` = llu.`LedgerId` 
                AND llu.`UserId` = @UserId
            LEFT JOIN `Ledger_LedgerDepartments` lld ON ll.`Id` = lld.`LedgerId`
            INNER JOIN `Platform_Departments` pd ON lld.`DepartmentId` = pd.`Id` 
            INNER JOIN `Platform_Regions` pr ON pd.`RegionId` = pr.`Id` 
            INNER JOIN `Ledger_LedgerTypes` llt ON ll.`LedgerTypeId` = llt.`Id`
            INNER JOIN `Platform_Departments` pd2 ON llt.`DepartmentId` = pd2.`Id`
            INNER JOIN `Platform_Regions` pr2 ON pd2.`RegionId` = pr2.`Id` 
            WHERE ll.`IsDeleted` = false
            AND llt.`IsDeleted` != true
            AND pd.`IsDeleted` != true 
            AND pr.`IsDeleted` != true
            AND pd2.`IsDeleted` != true
            AND pr.`Id` = @RegionId
            AND (ll.`BusinessLedgerType` IS NULL OR ll.`BusinessLedgerType` = 1)
            AND ll.`Name` NOT LIKE '%【测试】%'
            AND ll.`IsOnline` = true";

        var conditions = new StringBuilder();
        if (ledgerTypeId.HasValue)
        {
            conditions.AppendLine("AND llt.\"Id\" = @LedgerTypeId");
        }

        if (!string.IsNullOrEmpty(name))
        {
            conditions.AppendLine("AND ll.\"Name\" LIKE @Name");
        }

        if (!string.IsNullOrEmpty(runway))
        {
            conditions.AppendLine("AND ll.\"Runway\" = @Runway");
        }

        if (departmentId.HasValue)
        {
            conditions.Append(@" AND llt.`Id` = @DepartmentId ");
        }

        if (reminderInterval.HasValue)
        {
            conditions.Append(@" AND ll.`ReminderInterval` = @ReminderInterval ");
        }

        if (autoFillStatus.HasValue)
        {
            switch (autoFillStatus)
            {
                case MyLedgerDtoAutoFillStatus.NotNeedFill:
                    conditions.Append(@" AND (NOT (llu.`Permissions` && ARRAY[1,2,3])) ");
                    break;
                case MyLedgerDtoAutoFillStatus.NeedToFilled:
                    conditions.Append(@" AND (
		                                    llu.`Permissions` IS NOT NULL 
		                                    AND (llu.`Permissions` && ARRAY[1,2,3])
		                                    AND lld.`DepartmentId` = @BigDepartmentId
		                                    AND lld.`LitStatus` = 0
		                                    AND ll.`ReminderInterval` != 0
		                                ) ");
                    break;
                case MyLedgerDtoAutoFillStatus.NeedToNotFill:
                    conditions.Append(@" AND (
		                                    llu.`Permissions` IS NOT NULL 
		                                    AND (llu.`Permissions` && ARRAY[1,2,3])
		                                    AND lld.`DepartmentId` = @BigDepartmentId
		                                    AND lld.`LitStatus` != 0
		                                    AND ll.`ReminderInterval` != 0
		                                ) ");
                    break;
                case MyLedgerDtoAutoFillStatus.OtherFill:
                    conditions.Append(@" AND (
		                                    llu.`Permissions` IS NOT NULL 
		                                    AND (llu.`Permissions` && ARRAY[1,2,3])
		                                    AND ll.`ReminderInterval` = 0
		                                ) ");
                    break;
            }
        }

        if (litStatus.HasValue)
        {
            conditions.Append(@" AND lld.`LitStatus` = @LitStatus ");
        }

        conditions.AppendLine("" +
                              "\n\n            GROUP BY ll.\"Id\", ll.\"Name\", llt.\"Name\", ll.\"ReminderInterval\", ll.\"Runway\" " +
                              "),");

        var validDepartmentsCte = @"valid_departments AS (
            SELECT DISTINCT 
                lld.`LedgerId`
            FROM `Ledger_LedgerDepartments` lld
            INNER JOIN `Platform_Departments` lpd ON lld.`AuthDepartmentId` = lpd.`Id`
            INNER JOIN `Platform_Regions` lpr ON lpd.`RegionId` = lpr.`Id`
            INNER JOIN `Platform_Departments` apd ON lld.`DepartmentId` = apd.`Id`
            INNER JOIN `Platform_Regions` apr ON apd.`RegionId` = apr.`Id`
            WHERE apr.`Grade` = 2
        )";

        // 构建计数查询
        var countSql = new StringBuilder();
        countSql.AppendLine(baseCte);
        countSql.Append(conditions);
        countSql.AppendLine(validDepartmentsCte);
        countSql.AppendLine("SELECT COUNT(DISTINCT fl.LedgerId) FROM filtered_ledgers fl");
        countSql.AppendLine("LEFT JOIN valid_departments vd ON fl.LedgerId = vd.\"LedgerId\"");

        // 构建主查询
        var sql = new StringBuilder();
        sql.AppendLine(baseCte);
        sql.Append(conditions);
        sql.AppendLine(validDepartmentsCte);
        sql.AppendLine(@"SELECT DISTINCT
            fl.LedgerId,
            fl.LedgerName,
            fl.LedgerTypeName,
            fl.`LedgerDataLastUpdatedTime`,
            fl.`ReminderInterval`,
            fl.`Runway`,
            fl.`LitStatus`
        FROM filtered_ledgers fl
        LEFT JOIN valid_departments vd ON fl.LedgerId = vd.`LedgerId`
        ORDER BY fl.LedgerId DESC
        LIMIT @PageSize OFFSET @Offset");

        // 获取数据库连接
        var connection = await GetDbConnectionAsync();

        // 执行查询
        var totalCount = await connection.ExecuteScalarAsync<int>(countSql.ToString(), parameters);
        var result = (await connection.QueryAsync<LedgerDetailListView>(sql.ToString(), parameters)).ToList();

        return (totalCount, result);
    }
}