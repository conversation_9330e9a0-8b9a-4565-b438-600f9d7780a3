using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Inspur.Abp.Ledger.Core;
using Inspur.Abp.Ledger.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Inspur.Abp.Ledger.OceanBase.Dapper.Repository.Core;

public class LedgerAuditBatchStatisticsDapperRepository : LedgerAuditBatchStatisticsDapperRepositoryBase
{
    public LedgerAuditBatchStatisticsDapperRepository(IDbContextProvider<ILedgerDbContext> dbContextProvider) : base(
        dbContextProvider)
    {
    }

    public override async Task<(List<EachDepartmentReportingProgressView>, long)>
        GetEachDepartmentReportingProgressWithTotalCountByDate(
            Guid ledgerId, Guid bigDepartmentId, DateTime startTime, DateTime endTime, int skipCount,
            int maxResultCount)
    {
        var connection = await GetDbConnectionAsync();
        string dataSql = $@"
            SELECT
    ld.`DepartmentId` AS `DepartmentId`,
    d.`Name` AS `DepartmentName`,
    COALESCE(SUM(a.`NewDataTotal`), 0) AS `NewDataTotal`,
    COALESCE(SUM(a.`UpdateDataTotal`), 0) AS `UpdateDataTotal`,
    COALESCE(SUM(a.`DeleteDataTotal`), 0) AS `DeleteDataTotal`,
    COALESCE(SUM(a.`NewDataTotal`), 0) + COALESCE(SUM(a.`UpdateDataTotal`), 0) + COALESCE(SUM(a.`DeleteDataTotal`), 0) AS `AllUpdateTotal`
FROM
    `Ledger_LedgerDepartments` ld
LEFT JOIN (
    SELECT
        ab.`SubmitBigDepartmentId` AS `SubmitBigDepartmentId`,
        SUM(a.`NewDataTotal`) AS `NewDataTotal`,
        SUM(a.`UpdateDataTotal`) AS `UpdateDataTotal`,
        SUM(a.`DeleteDataTotal`) AS `DeleteDataTotal`
    FROM
        `Ledger_LedgerAuditBatchStatistics` a
    JOIN `Ledger_LedgerAuditBatches` ab ON a.`Id` = ab.`Id`
    WHERE
        ab.`LedgerId` = @LedgerId
        AND ab.`AuditCompletionTime` >= @StartTime
        AND ab.`AuditCompletionTime` <= @EndTime
        AND ab.`Status` = 1
    GROUP BY
        ab.`SubmitBigDepartmentId`
) a ON ld.`DepartmentId` = a.`SubmitBigDepartmentId`
JOIN `Platform_Departments` d ON ld.`DepartmentId` = d.`Id`
WHERE
    ld.`LedgerId` = @LedgerId
    AND (FIND_IN_SET(1, ld.`Permissions`) OR FIND_IN_SET(2, ld.`Permissions`) OR FIND_IN_SET(3, ld.`Permissions`))
    AND (
        ld.`DepartmentId` = @BigDepartmentId
        OR ld.`AuthDepartmentId` = @BigDepartmentId
    )
GROUP BY
    ld.`DepartmentId`,
    d.`Name`
ORDER BY
    `AllUpdateTotal` DESC
LIMIT @SkipCount, @MaxResultCount";

        string countSql = @"
        SELECT COUNT(*)
        FROM (
            SELECT
                ld.`DepartmentId` AS `DepartmentId`
            FROM
                `Ledger_LedgerDepartments` ld
            LEFT JOIN (
                SELECT
                    ab.`SubmitBigDepartmentId` AS `SubmitBigDepartmentId`
                FROM
                    `Ledger_LedgerAuditBatchStatistics` a
                JOIN `Ledger_LedgerAuditBatches` ab ON a.`Id` = ab.`Id`
                WHERE
                    ab.`LedgerId` = @LedgerId
                    AND ab.`AuditCompletionTime` >= @StartTime
                    AND ab.`AuditCompletionTime` <= @EndTime
                    AND ab.`Status` = 1
                GROUP BY
                    ab.`SubmitBigDepartmentId`
            ) a ON ld.`DepartmentId` = a.`SubmitBigDepartmentId`
            WHERE
                ld.`LedgerId` = @LedgerId
                AND (FIND_IN_SET(1, ld.`Permissions`) OR FIND_IN_SET(2, ld.`Permissions`) OR FIND_IN_SET(3, ld.`Permissions`))
                AND (
                    ld.`DepartmentId` = @BigDepartmentId
                    OR ld.`AuthDepartmentId` = @BigDepartmentId
                )
        ) AS TotalRows";

        var parameters = new
        {
            LedgerId = ledgerId,
            BigDepartmentId = bigDepartmentId,
            StartTime = startTime,
            EndTime = endTime,
            SkipCount = skipCount,
            MaxResultCount = maxResultCount
        };

        using (var multi = await connection.QueryMultipleAsync(dataSql + ";" + countSql, parameters))
        {
            var data = (await multi.ReadAsync<EachDepartmentReportingProgressView>()).ToList();
            var totalCount = await multi.ReadSingleAsync<long>();
            return (data, totalCount);
        }
    }

    public override async Task<(List<EachDepartmentReportingProgressView>, long)>
        GetEachDepartmentReportingProgressWithTotalCountByTaskItem(
            Guid ledgerId, Guid taskItemId, List<Guid> largeDepartmentIds, int skipCount,
            int maxResultCount)
    {
        var connection = await GetDbConnectionAsync();
        string dataSql = $@"
            SELECT
    ld.`DepartmentId` AS `DepartmentId`,
    d.`Name` AS `DepartmentName`,
    COALESCE(SUM(a.`NewDataTotal`), 0) AS `NewDataTotal`,
    COALESCE(SUM(a.`UpdateDataTotal`), 0) AS `UpdateDataTotal`,
    COALESCE(SUM(a.`DeleteDataTotal`), 0) AS `DeleteDataTotal`,
    COALESCE(SUM(a.`NewDataTotal`), 0) + COALESCE(SUM(a.`UpdateDataTotal`), 0) + COALESCE(SUM(a.`DeleteDataTotal`), 0) AS `AllUpdateTotal`,
    CASE 
        WHEN region.`Grade` IN (2, 3) THEN region.`Name`
        WHEN region.`Grade` IN (4, 5, 6) THEN parent_region.`Name`
    END AS `BelongRegionName`
FROM
    `Ledger_LedgerDepartments` ld
LEFT JOIN (
    SELECT
        ab.`SubmitBigDepartmentId` AS `SubmitBigDepartmentId`,
        SUM(a.`NewDataTotal`) AS `NewDataTotal`,
        SUM(a.`UpdateDataTotal`) AS `UpdateDataTotal`,
        SUM(a.`DeleteDataTotal`) AS `DeleteDataTotal`
    FROM
        `Ledger_LedgerAuditBatchStatistics` a
    JOIN `Ledger_LedgerAuditBatches` ab ON a.`Id` = ab.`Id`
    WHERE
        ab.`LedgerId` = @LedgerId
        AND ab.`TaskItemId` = @TaskItemId
        AND ab.`Status` = 1
    GROUP BY
        ab.`SubmitBigDepartmentId`
) a ON ld.`DepartmentId` = a.`SubmitBigDepartmentId`
JOIN `Platform_Departments` d ON ld.`DepartmentId` = d.`Id`
LEFT JOIN `Platform_Regions` region ON d.`RegionId` = region.`Id`
LEFT JOIN `Platform_Regions` parent_region ON region.`ParentId` = parent_region.`Id`
WHERE
    ld.`LedgerId` = @LedgerId
    AND (FIND_IN_SET(1, ld.`Permissions`) OR FIND_IN_SET(2, ld.`Permissions`) OR FIND_IN_SET(3, ld.`Permissions`))
    AND ld.`DepartmentId` IN @LargeDepartmentIds
GROUP BY
    ld.`DepartmentId`,
    d.`Name`,
    `BelongRegionName`";

        var departmentIsd = largeDepartmentIds.Skip(skipCount).Take(maxResultCount).ToList();

        if (!departmentIsd.Any())
            return new(new List<EachDepartmentReportingProgressView>(),
                0);

        var parameters = new
        {
            LedgerId = ledgerId,
            LargeDepartmentIds = departmentIsd,
            TaskItemId = taskItemId
        };

        var data = (await connection.QueryAsync<EachDepartmentReportingProgressView>(dataSql, parameters)).ToList();

        var sortedData = new List<EachDepartmentReportingProgressView>();

        foreach (var departmentId in departmentIsd)
        {
            var matchingData = data.Where(d => d.DepartmentId == departmentId).ToList();

            sortedData.AddRange(matchingData);
        }

        return (sortedData, largeDepartmentIds.Count);
    }

    public override async Task<(List<CurrentDepartmentPersonnelReportingProgressView>, long)>
        GetCurrentDepartmentPersonnelReportingProgressWithTotalCountByDate(Guid ledgerId, Guid bigDepartmentId,
            DateTime startTime, DateTime endTime, int skipCount, int maxResultCount)
    {
        var connection = await GetDbConnectionAsync();
        string dataSql = $@"SELECT
    lu.`UserId` AS `ReporterId`,
    d.`Name` AS `ReporterName`,
    COALESCE(SUM(a.`NewDataTotal`), 0) AS `NewDataTotal`,
    COALESCE(SUM(a.`UpdateDataTotal`), 0) AS `UpdateDataTotal`,
    COALESCE(SUM(a.`DeleteDataTotal`), 0) AS `DeleteDataTotal`,
    COALESCE(SUM(a.`NewDataTotal`), 0) + COALESCE(SUM(a.`UpdateDataTotal`), 0) + COALESCE(SUM(a.`DeleteDataTotal`), 0) AS `AllUpdateTotal`
FROM
    `Ledger_LedgerUsers` lu
LEFT JOIN (
    SELECT
        ab.`SubmitUserId` AS `SubmitUserId`,
        SUM(COALESCE(a.`NewDataTotal`, 0)) AS `NewDataTotal`,
        SUM(COALESCE(a.`UpdateDataTotal`, 0)) AS `UpdateDataTotal`,
        SUM(COALESCE(a.`DeleteDataTotal`, 0)) AS `DeleteDataTotal`
    FROM
        `Ledger_LedgerAuditBatchStatistics` a
    JOIN `Ledger_LedgerAuditBatches` ab ON a.`Id` = ab.`Id`
    WHERE
        ab.`LedgerId` = @LedgerId
        AND ab.`AuditCompletionTime` >= @StartTime
        AND ab.`AuditCompletionTime` <= @EndTime
        AND ab.`Status` = 1
    GROUP BY
        ab.`SubmitUserId`
) a ON lu.`UserId` = a.`SubmitUserId`
JOIN `Platform_Users` d ON lu.`UserId` = d.`Id`
WHERE
    lu.`LedgerId` = @LedgerId
    AND (FIND_IN_SET(1, lu.`Permissions`) OR FIND_IN_SET(2, lu.`Permissions`) OR FIND_IN_SET(3, lu.`Permissions`))
    AND (
        lu.`DepartmentId` = @BigDepartmentId
    )
GROUP BY
    lu.`UserId`,
    d.`Name`
ORDER BY
    `AllUpdateTotal` DESC
LIMIT @SkipCount, @MaxResultCount
";

        string countSql = @"SELECT
    COUNT(*) AS TotalCount
FROM
(
    SELECT
        lu.`UserId` AS `ReporterId`,
        d.`Name` AS `ReporterName`,
        COALESCE(SUM(a.`NewDataTotal`), 0) AS `NewDataTotal`,
        COALESCE(SUM(a.`UpdateDataTotal`), 0) AS `UpdateDataTotal`,
        COALESCE(SUM(a.`DeleteDataTotal`), 0) AS `DeleteDataTotal`,
        COALESCE(SUM(a.`NewDataTotal`), 0) + COALESCE(SUM(a.`UpdateDataTotal`), 0) + COALESCE(SUM(a.`DeleteDataTotal`), 0) AS `AllUpdateTotal`
    FROM
        `Ledger_LedgerUsers` lu
    LEFT JOIN (
        SELECT
            ab.`SubmitUserId` AS `SubmitUserId`,
            SUM(a.`NewDataTotal`) AS `NewDataTotal`,
            SUM(a.`UpdateDataTotal`) AS `UpdateDataTotal`,
            SUM(a.`DeleteDataTotal`) AS `DeleteDataTotal`
        FROM
            `Ledger_LedgerAuditBatchStatistics` a
        JOIN `Ledger_LedgerAuditBatches` ab ON a.`Id` = ab.`Id`
        WHERE
            ab.`LedgerId` = @LedgerId
            AND ab.`AuditCompletionTime` >= @StartTime
            AND ab.`AuditCompletionTime` <= @EndTime
            AND ab.`Status` = 1
        GROUP BY
            ab.`SubmitUserId`
    ) a ON lu.`UserId` = a.`SubmitUserId`
    JOIN `Platform_Users` d ON lu.`UserId` = d.`Id`
    WHERE
        lu.`LedgerId` = @LedgerId
        AND (FIND_IN_SET(1, lu.`Permissions`) OR FIND_IN_SET(2, lu.`Permissions`) OR FIND_IN_SET(3, lu.`Permissions`))
        AND (
            lu.`DepartmentId` = @BigDepartmentId
        )
    GROUP BY
        lu.`UserId`,
        d.`Name`
) AS subquery;
";

        var parameters = new
        {
            LedgerId = ledgerId,
            BigDepartmentId = bigDepartmentId,
            StartTime = startTime,
            EndTime = endTime,
            SkipCount = skipCount,
            MaxResultCount = maxResultCount
        };

        using (var multi = await connection.QueryMultipleAsync(dataSql + ";" + countSql, parameters))
        {
            var data = (await multi.ReadAsync<CurrentDepartmentPersonnelReportingProgressView>()).ToList();
            var totalCount = await multi.ReadSingleAsync<long>();
            return (data, totalCount);
        }
    }

    public override async Task<(List<CurrentDepartmentPersonnelReportingProgressView>, long)>
        GetCurrentDepartmentPersonnelReportingProgressWithTotalCountByTaskItem(Guid ledgerId,
            Guid taskItemId, Guid bigDepartmentId, int skipCount, int maxResultCount)
    {
        var connection = await GetDbConnectionAsync();
        string dataSql = $@"SELECT
    lu.`UserId` AS `ReporterId`,
    d.`Name` AS `ReporterName`,
    COALESCE(SUM(a.`NewDataTotal`), 0) AS `NewDataTotal`,
    COALESCE(SUM(a.`UpdateDataTotal`), 0) AS `UpdateDataTotal`,
    COALESCE(SUM(a.`DeleteDataTotal`), 0) AS `DeleteDataTotal`,
    COALESCE(SUM(a.`NewDataTotal`), 0) + COALESCE(SUM(a.`UpdateDataTotal`), 0) + COALESCE(SUM(a.`DeleteDataTotal`), 0) AS `AllUpdateTotal`
FROM
    `Ledger_LedgerUsers` lu
LEFT JOIN (
    SELECT
        ab.`SubmitUserId` AS `SubmitUserId`,
        SUM(COALESCE(a.`NewDataTotal`, 0)) AS `NewDataTotal`,
        SUM(COALESCE(a.`UpdateDataTotal`, 0)) AS `UpdateDataTotal`,
        SUM(COALESCE(a.`DeleteDataTotal`, 0)) AS `DeleteDataTotal`
    FROM
        `Ledger_LedgerAuditBatchStatistics` a
    JOIN `Ledger_LedgerAuditBatches` ab ON a.`Id` = ab.`Id`
    WHERE
        ab.`LedgerId` = @LedgerId
        AND ab.`TaskItemId` = @TaskItemId
		AND ab.`SubmitBigDepartmentId`= @BigDepartmentId
        AND ab.`Status` = 1
    GROUP BY
        ab.`SubmitUserId`
) a ON lu.`UserId` = a.`SubmitUserId`
JOIN `Platform_Users` d ON lu.`UserId` = d.`Id`
WHERE
    lu.`LedgerId` = @LedgerId
    AND (FIND_IN_SET(1, lu.`Permissions`) OR FIND_IN_SET(2, lu.`Permissions`) OR FIND_IN_SET(3, lu.`Permissions`))
    AND (
        lu.`DepartmentId` = @BigDepartmentId
    )
    AND d.`IsDeleted` != true
GROUP BY
    lu.`UserId`,
    d.`Name`
ORDER BY
    `AllUpdateTotal` DESC
LIMIT @SkipCount, @MaxResultCount
";

        string countSql = @"SELECT
    COUNT(*) AS TotalCount
FROM
(
    SELECT
        lu.`UserId` AS `ReporterId`,
        d.`Name` AS `ReporterName`,
        COALESCE(SUM(a.`NewDataTotal`), 0) AS `NewDataTotal`,
        COALESCE(SUM(a.`UpdateDataTotal`), 0) AS `UpdateDataTotal`,
        COALESCE(SUM(a.`DeleteDataTotal`), 0) AS `DeleteDataTotal`,
        COALESCE(SUM(a.`NewDataTotal`), 0) + COALESCE(SUM(a.`UpdateDataTotal`), 0) + COALESCE(SUM(a.`DeleteDataTotal`), 0) AS `AllUpdateTotal`
    FROM
        `Ledger_LedgerUsers` lu
    LEFT JOIN (
        SELECT
            ab.`SubmitUserId` AS `SubmitUserId`,
            SUM(a.`NewDataTotal`) AS `NewDataTotal`,
            SUM(a.`UpdateDataTotal`) AS `UpdateDataTotal`,
            SUM(a.`DeleteDataTotal`) AS `DeleteDataTotal`
        FROM
            `Ledger_LedgerAuditBatchStatistics` a
        JOIN `Ledger_LedgerAuditBatches` ab ON a.`Id` = ab.`Id`
        WHERE
            ab.`LedgerId` = @LedgerId
            AND ab.`TaskItemId` = @TaskItemId
		    AND ab.`SubmitBigDepartmentId`= @BigDepartmentId
            AND ab.`Status` = 1
        GROUP BY
            ab.`SubmitUserId`
    ) a ON lu.`UserId` = a.`SubmitUserId`
    JOIN `Platform_Users` d ON lu.`UserId` = d.`Id`
    WHERE
        lu.`LedgerId` = @LedgerId
        AND (FIND_IN_SET(1, lu.`Permissions`) OR FIND_IN_SET(2, lu.`Permissions`) OR FIND_IN_SET(3, lu.`Permissions`))
        AND (
            lu.`DepartmentId` = @BigDepartmentId
        )
        AND d.`IsDeleted` != true
    GROUP BY
        lu.`UserId`,
        d.`Name`
) AS subquery;
";

        var parameters = new
        {
            LedgerId = ledgerId,
            BigDepartmentId = bigDepartmentId,
            TaskItemId = taskItemId,
            SkipCount = skipCount,
            MaxResultCount = maxResultCount
        };

        using (var multi = await connection.QueryMultipleAsync(dataSql + ";" + countSql, parameters))
        {
            var data = (await multi.ReadAsync<CurrentDepartmentPersonnelReportingProgressView>()).ToList();
            var totalCount = await multi.ReadSingleAsync<long>();
            return (data, totalCount);
        }
    }

    public override async Task<bool>
        IsExistsCurrentDepartmentPersonnelReportedDataByDateAsync(Guid ledgerId, Guid bigDepartmentId,
            DateTime startTime, DateTime endTime)
    {
        var connection = await GetDbConnectionAsync();
        string countSql = @"SELECT
    1
FROM
    ""Ledger_LedgerUsers"" lu
LEFT JOIN (
    SELECT
        ab.`SubmitUserId` AS `SubmitUserId`,
        SUM(COALESCE(a.`NewDataTotal`, 0)) AS `NewDataTotal`,
        SUM(COALESCE(a.`UpdateDataTotal`, 0)) AS `UpdateDataTotal`,
        SUM(COALESCE(a.`DeleteDataTotal`, 0)) AS `DeleteDataTotal`
    FROM
        `Ledger_LedgerAuditBatches` ab
    LEFT JOIN `Ledger_LedgerAuditBatchStatistics` a ON a.`Id` = ab.`Id`
    WHERE
        ab.`LedgerId` = @LedgerId
        AND ab.`AuditCompletionTime` >= @StartTime
        AND ab.`AuditCompletionTime` <= @EndTime
        AND ab.`Status` = 1
    GROUP BY
        ab.`SubmitUserId`
) a ON lu.`UserId` = a.`SubmitUserId`
JOIN `Platform_Users` d ON lu.`UserId` = d.`Id`
WHERE
    lu.`LedgerId` = @LedgerId
    AND (FIND_IN_SET(1, lu.`Permissions`) OR FIND_IN_SET(2, lu.`Permissions`) OR FIND_IN_SET(3, lu.`Permissions`))
    AND lu.`DepartmentId` = @BigDepartmentId
GROUP BY
    lu.`UserId`,
    d.`Name`
HAVING
    COALESCE(SUM(a.`NewDataTotal`), 0) + COALESCE(SUM(a.`UpdateDataTotal`), 0) + COALESCE(SUM(a.`DeleteDataTotal`), 0) > 0
LIMIT 1;
";
        var parameters = new
        {
            LedgerId = ledgerId,
            BigDepartmentId = bigDepartmentId,
            StartTime = startTime,
            EndTime = endTime,
        };

        return (await connection.QueryAsync<bool>(countSql, parameters)).Any();
    }

    public override async Task<bool>
        IsExistsCurrentDepartmentPersonnelReportedDataByTaskItemAsync(Guid ledgerId, Guid bigDepartmentId,
            Guid taskItemId)
    {
        var connection = await GetDbConnectionAsync();
        string countSql = @"SELECT
    1
FROM
    `Ledger_LedgerUsers` lu
LEFT JOIN (
    SELECT
        ab.`SubmitUserId` AS `SubmitUserId`,
        SUM(COALESCE(a.`NewDataTotal`, 0)) AS `NewDataTotal`,
        SUM(COALESCE(a.`UpdateDataTotal`, 0)) AS `UpdateDataTotal`,
        SUM(COALESCE(a.`DeleteDataTotal`, 0)) AS `DeleteDataTotal`
    FROM
        `Ledger_LedgerAuditBatches` ab
    LEFT JOIN `Ledger_LedgerAuditBatchStatistics` a ON a.`Id` = ab.`Id`
    WHERE
        ab.`LedgerId` = @LedgerId
        AND ab.`TaskItemId` = @TaskItemId
		AND ab.`SubmitBigDepartmentId` = @BigDepartmentId
        AND ab.`Status` = 1
    GROUP BY
        ab.`SubmitUserId`
) a ON lu.`UserId` = a.`SubmitUserId`
JOIN `Platform_Users` d ON lu.`UserId` = d.`Id`
WHERE
    lu.`LedgerId` = @LedgerId
    AND (FIND_IN_SET(1, lu.`Permissions`) OR FIND_IN_SET(2, lu.`Permissions`) OR FIND_IN_SET(3, lu.`Permissions`))
    AND lu.`DepartmentId` = @BigDepartmentId
GROUP BY
    lu.`UserId`,
    d.`Name`
HAVING
    COALESCE(SUM(a.`NewDataTotal`), 0) + COALESCE(SUM(a.`UpdateDataTotal`), 0) + COALESCE(SUM(a.`DeleteDataTotal`), 0) > 0
LIMIT 1;
";
        var parameters = new
        {
            LedgerId = ledgerId,
            BigDepartmentId = bigDepartmentId,
            TaskItemId = taskItemId
        };

        return (await connection.QueryAsync<bool>(countSql, parameters)).Any();
    }

    public override async Task<List<AuditBatchIdByLedgerIdView>> GetAuditBatchIdsByLedgerId(Guid ledgerId, string deadline)
    {
        string dataSql = $@"
        SELECT 
            wti.""Id"" as ""TaskItemId"",
            -- wti.""Deadline"" as ""Deadline"",
            lab.""Id"" as ""AuditBatchId"",
            lab.""CreationTime"",
            lab.""SubmitBigDepartmentId"" as ""SubmitBigDepartmentId"",
            lab.""Type"" as ""LedgerAuditBatchType"",
            CASE 
                WHEN lab.""AuditCompletionTime"" <= wti.""Deadline"" THEN 0  -- NoOverdue
                ELSE 1  -- ExceedTime
            END as ""FillExpiredState""
        FROM ""Platform_WFPlanTasks"" wpt
        JOIN ""Platform_WFTaskItems"" wti ON wti.""PlanTaskId"" = wpt.""Id""
        JOIN ""Ledger_LedgerAuditBatches"" lab ON lab.""TaskItemId"" = wti.""Id""
        WHERE wpt.""BindObjectId"" = @LedgerId
        AND wti.""Deadline"" >= @Deadline
        ORDER BY 
            wti.""Id"",
            lab.""CreationTime"" ASC;
		        ";

      
        var parameters = new
        {
            LedgerId = ledgerId.ToString(),
            Deadline = DateTime.Parse(deadline)
        };

        using (var multi = await DbConnection.QueryMultipleAsync(dataSql, parameters))
        {
            var data = (await multi.ReadAsync<AuditBatchIdByLedgerIdView>()).ToList();
            return data;
        }
    }

    /// <summary>
    /// 获取跨年（2024-2025年）每周为周期的台账部门
    /// </summary>
    /// <returns></returns>
    public override async Task<(List<LedgerTaskItemView>, long TotalCount)> GetWeeksLedgerViewByFilter(int skip, int take)
    {
        var connection = await GetDbConnectionAsync();
        string dataSql = $@"
        SELECT
            ledger.`Id`,
            ledger.`Name`,
            -- ledger.`ReminderInterval`,
            -- plant.`LatestTaskItemId`,
            -- items.`Deadline`,
            ldep.`DepartmentId`,
            ldep.`AuthDepartmentId` 
        FROM
            `Ledger_LedgerDepartments` AS ldep
            INNER JOIN `Ledger_Ledgers` AS ledger ON ldep.`LedgerId` = ledger.`Id`
            INNER JOIN `Platform_WFPlanTasks` AS plant ON plant.`BindObjectId` = CAST(ledger.`Id` AS CHAR)
            INNER JOIN `Platform_WFTaskItems` AS items ON plant.`LatestTaskItemId` = items.`Id`
        WHERE
            ledger.`ReminderInterval` = 2 
            AND ledger.`IsOnline` = TRUE 
            AND plant.`LatestTaskItemId` IS NOT NULL 
            AND items.`Deadline` >= @BeforTime
            AND items.`Deadline` <= @LaterTime
        ORDER BY
            ldep.`DepartmentId` ASC
        LIMIT @Skip, @Take";
        
        string countSql = $@"
        SELECT
            count(*)
        FROM
            `Ledger_LedgerDepartments` AS ldep
            INNER JOIN `Ledger_Ledgers` AS ledger ON ldep.`LedgerId` = ledger.`Id`
            INNER JOIN `Platform_WFPlanTasks` AS plant ON plant.`BindObjectId` = CAST(ledger.`Id` AS CHAR)
            INNER JOIN `Platform_WFTaskItems` AS items ON plant.`LatestTaskItemId` = items.`Id`
        WHERE
            ledger.`ReminderInterval` = 2 
            AND ledger.`IsOnline` = TRUE 
            AND plant.`LatestTaskItemId` IS NOT NULL 
            AND items.`Deadline` >= @BeforTime
            -- AND items.`Deadline` <= @LaterTime
        ORDER BY
            ldep.`DepartmentId` ASC;
        ";

      
        var parameters = new
        {
            BeforTime = DateTime.Parse("2024-12-30 00:00:01"),
            // LaterTime = DateTime.Parse("2025-01-05 23:59:59"),
            Skip = skip,
            Take = take
        };

        using (var multi = await connection.QueryMultipleAsync(dataSql + ";" + countSql, parameters))
        {
            var data = (await multi.ReadAsync<LedgerTaskItemView>()).ToList();
            var totalCount = await multi.ReadSingleAsync<long>();
            return (data, totalCount);
        }
    }
    
    
    /// <summary>
    /// 获取2025年除开 每周 为周期以外 且 周期>0（有提醒周期）的台账部门
    /// </summary>
    /// <returns></returns>
    public override async Task<(List<LedgerTaskItemView>, long TotalCount)> GetAllLedgerViewByFilter(int skip, int take)
    {
        var connection = await GetDbConnectionAsync();
        string dataSql = $@"
        SELECT
	        ledger.`Id`,
	        ledger.`Name`,
	        -- ledger.`ReminderInterval`,
	        -- plant.`LatestTaskItemId`,
	        -- items.`Deadline`,
	        ldep.`DepartmentId`,
	        ldep.`AuthDepartmentId` 
        FROM
	        `Ledger_LedgerDepartments` AS ldep
	        INNER JOIN `Ledger_Ledgers` AS ledger ON ldep.`LedgerId` = ledger.`Id`
	        INNER JOIN `Platform_WFPlanTasks` AS plant ON plant.`BindObjectId` = CAST(ledger.`Id` AS CHAR)
	        INNER JOIN `Platform_WFTaskItems` AS items ON plant.`LatestTaskItemId` = items.`Id` 
        WHERE
	        ( ledger.`ReminderInterval` > 0 AND ledger.`ReminderInterval` != 2 ) 
	        AND ledger.`IsOnline` = TRUE 
	        AND plant.`LatestTaskItemId` IS NOT NULL 
	        AND items.`Deadline` >= @BeforTime
        ORDER BY
	        ldep.`DepartmentId` ASC 
	        LIMIT @Skip, @Take";
        
        string countSql = $@"
        SELECT
	        count(*)
        FROM
	        `Ledger_LedgerDepartments` AS ldep
	        INNER JOIN `Ledger_Ledgers` AS ledger ON ldep.`LedgerId` = ledger.`Id`
	        INNER JOIN `Platform_WFPlanTasks` AS plant ON plant.`BindObjectId` = CAST(ledger.`Id` AS CHAR)
	        INNER JOIN `Platform_WFTaskItems` AS items ON plant.`LatestTaskItemId` = items.`Id` 
        WHERE
	        ( ledger.`ReminderInterval` > 0 AND ledger.`ReminderInterval` != 2 ) 
	        AND ledger.`IsOnline` = TRUE 
	        AND plant.`LatestTaskItemId` IS NOT NULL 
	        AND items.`Deadline` >= @BeforTime
        ORDER BY
	        ldep.`DepartmentId` ASC ";

      
        var parameters = new
        {
            BeforTime = DateTime.Parse("2025-1-01 00:00:00"),
            Skip = skip,
            Take = take
        };

        using (var multi = await connection.QueryMultipleAsync(dataSql + ";" + countSql, parameters))
        {
            var data = (await multi.ReadAsync<LedgerTaskItemView>()).ToList();
            var totalCount = await multi.ReadSingleAsync<long>();
            return (data, totalCount);
        }
    }
}