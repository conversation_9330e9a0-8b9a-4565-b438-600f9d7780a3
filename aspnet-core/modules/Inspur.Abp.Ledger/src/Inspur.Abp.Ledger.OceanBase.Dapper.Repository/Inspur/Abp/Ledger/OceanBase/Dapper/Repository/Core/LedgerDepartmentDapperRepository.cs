using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using <PERSON>pper;
using Inspur.Abp.Ledger.Core;
using Inspur.Abp.Ledger.EntityFrameworkCore;
using Inspur.Abp.Platform.Organizational;
using Volo.Abp.EntityFrameworkCore;

namespace Inspur.Abp.Ledger.OceanBase.Dapper.Repository.Core;

public class LedgerDepartmentDapperRepository : LedgerDepartmentDapperRepositoryBase
{
    public LedgerDepartmentDapperRepository(IDbContextProvider<ILedgerDbContext> dbContextProvider) : base(
        dbContextProvider)
    {
    }

    public override async Task<List<DepartmentAuthorizedByRegionCodeView>> GetDepartmentAuthorizedByRegionCodeView(
        string regionCode, List<Guid> selectLedgerIds = null, List<Guid> excludeLedgerIds = null)
    {
        // string sql =
        //     @"select pr.""TreeCode"" as ""RegionCode"",pr.""Name"" as ""RegionName"",lld.""LedgerId"",ll.""IsOnline"",llt.""Runway"",lti.""Name"" as ""TableName"" from ""Ledger_LedgerDepartments"" lld left join ""Platform_Departments"" pd on lld.""DepartmentId""=pd.""Id"" left join ""Platform_Regions"" pr on pd.""RegionId""=pr.""Id""
        // left join ""Ledger_Ledgers"" ll on lld.""LedgerId""=ll.""Id"" left join ""Ledger_TableInfos"" lti on ll.""Id""=lti.""Id"" LEFT JOIN ""Ledger_LedgerTypes"" llt on ll.""LedgerTypeId""=llt.""Id""  
        // WHERE  pr.""TreeCode"" like @RegionCode";
        string sql =
            @"select pr.`TreeCode` as `RegionCode`, pr.`Name` as `RegionName`, lld.`LedgerId`, ll.`Name` AS `LedgerName`, ll.`IsOnline`, ll.`Runway`, lti.`Name` as `TableName` from `Ledger_LedgerDepartments` lld left join `Platform_Departments` pd on lld.`DepartmentId`=pd.`Id` left join `Platform_Regions` pr on pd.`RegionId`=pr.`Id`
        left join `Ledger_Ledgers` ll on lld.`LedgerId`=ll.`Id` left join `Ledger_TableInfos` lti on ll.`Id`=lti.`Id` 
        WHERE  pr.`TreeCode` like @RegionCode";

        if (selectLedgerIds != null && selectLedgerIds.Any())
        {
            sql += " and lld.`LedgerId` IN @SelectLedgerIds";
        }

        if (excludeLedgerIds != null && excludeLedgerIds.Any())
        {
            sql += " AND lld.`LedgerId` NOT IN @ExcludeLedgerIds";
        }


        var parameters = new
        {
            RegionCode = $"{regionCode}%",
            SelectLedgerIds = selectLedgerIds?.ToArray(),
            ExcludeLedgerIds = excludeLedgerIds?.ToArray()
        };

        var connection = await GetDbConnectionAsync();
        return (await connection.QueryAsync<DepartmentAuthorizedByRegionCodeView>(sql, parameters)).ToList();
    }

    /// <summary>
    /// 获取区县被市级授权得台账数量
    /// </summary>
    /// <returns></returns>
    public override async Task<Dictionary<string, int>> GetDistrictLedgerCountByCityAuthorizedAsync(string cityRegionCode)
    {
        string sql = @$"
SELECT `RegionName`, count(1) AS `Count` FROM (

SELECT pr1.`Name` AS `RegionName`, ll.`Id` as `LedgerId` FROM `Ledger_LedgerDepartments` lld

INNER JOIN `Ledger_Ledgers` ll on lld.`LedgerId`=ll.`Id`

INNER JOIN `Ledger_LedgerTypes` llt on ll.`LedgerTypeId`=llt.`Id`

INNER JOIN `Platform_Departments` pd on llt.`DepartmentId`=pd.`Id` 

INNER JOIN `Platform_Regions` pr on pr.`Id`=pd.`RegionId`

INNER JOIN `Platform_Departments` pd1 on lld.`DepartmentId`=pd1.`Id`

INNER JOIN `Platform_Regions` pr1 on pr1.`Id`=pd1.`RegionId`

INNER JOIN `Platform_Regions` pr2 on pr2.`Id`=pr1.`ParentId`

WHERE pr.`Grade`=2 and pr1.`Grade`=3 and ll.`IsOnline`=true

AND pd.`IsDeleted`=false and pr.`IsDeleted`=false AND ll.`IsDeleted`=false AND llt.`IsDeleted`=false AND pr1.`IsDeleted`=false AND pr2.`IsDeleted`=false

AND pd.`ParentId` is not null and pr2.`TreeCode`='{cityRegionCode}' 

AND ll.`Name` like '【%'

GROUP BY pr1.`Name`, ll.`Id`) t

GROUP BY t.`RegionName`
";

        var connection = await GetDbConnectionAsync();
        var result = await connection.QueryAsync<RegionLedgerCountView>(sql);
        return result.ToDictionary(r => r.RegionName, r => r.Count);
    }


    /// <summary>
    /// 获取区县被市级授权得台账表名
    /// </summary>
    /// <returns></returns>
    public override async Task<Dictionary<string, List<string>>> GetDistrictTableNameByCityAuthorizedAsync(string cityRegionCode)
    {
        string sql = @$"
SELECT pr1.`Name` AS `RegionName`, ll.`Id` AS `LedgerId`, ll.`Name` AS `TableName`, ll.`Runway` FROM `Ledger_LedgerDepartments` lld

INNER JOIN `Ledger_Ledgers` ll on lld.`LedgerId`=ll.`Id`

INNER JOIN `Ledger_LedgerTypes` llt on ll.`LedgerTypeId`=llt.`Id`

INNER JOIN `Platform_Departments` pd on llt.`DepartmentId`=pd.`Id` 

INNER JOIN `Platform_Regions` pr on pr.`Id`=pd.`RegionId`

INNER JOIN `Platform_Departments` pd1 on lld.`DepartmentId`=pd1.`Id`

INNER JOIN `Platform_Regions` pr1 on pr1.`Id`=pd1.`RegionId`

INNER JOIN `Platform_Regions` pr2 on pr2.`Id`=pr1.`ParentId`

WHERE pr.`Grade`=2 and pr1.`Grade`=3 and ll.`IsOnline`=true

AND pd.`IsDeleted`=false and pr.`IsDeleted`=false AND ll.`IsDeleted`=false AND llt.`IsDeleted`=false AND pr1.`IsDeleted`=false AND pr2.`IsDeleted`=false

AND pd.`ParentId` is not null and pr2.`TreeCode`='{cityRegionCode}' 

AND ll.`Name` like '【%'

GROUP BY pr1.`Name`, ll.`Id`, ll.`Name`, ll.`Runway`
ORDER BY `RegionName` ASC
";

        var connection = await GetDbConnectionAsync();
        var result = (await connection.QueryAsync<RegionTableNameView>(sql)).ToList();
        return result
            .GroupBy(f => f.RegionName)
            .ToDictionary(f => f.Key, f => f.Select(r => r.TableName).ToList());
    }

    /// <summary>
    /// 获取区县台账数量及授权部门数量
    /// </summary>
    /// <returns></returns>
    public override async Task<List<DistrictLedgerCountAndAuthDepartmentCountView>>
        GetDistrictLedgerCountAndAuthDepartmentCountAsync(string cityRegionCode)
    {
        string sql = @$"
SELECT `RegionName`, COUNT(`AuthDepartment`) AS `AuthDepartmentCount`, SUM(`LedgerCount`) AS `LedgerCount` FROM (
		SELECT `RegionName`, `AuthDepartment`, count(`TableName`) AS `LedgerCount` FROM (
				SELECT pr.`Name` AS `RegionName`,
									llt.`Name` as `AuthDepartment`,
									lti.`Name` as `TableName`
						FROM `Ledger_LedgerTypes` llt
						INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
						INNER JOIN `Platform_Regions` pr ON pr.`Id` = pd.`RegionId`  
						INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id`
						INNER JOIN `Ledger_TableInfos` lti ON ll.`Id`=lti.`Id`
						WHERE pr.`Grade` = 3 and pr.`TreeCode` like '{cityRegionCode}%' AND ll.`IsOnline`=true AND ll.`Name` like '【%'
						AND ll.`IsDeleted`=false  AND llt.`IsDeleted`=false AND pd.`IsDeleted`=false AND pr.`IsDeleted`=false
				
				UNION
				
				SELECT pr1.`Name` AS `RegionName`,
									llt.`Name` as `AuthDepartment`,
									lti.`Name` as `TableName`
						FROM `Ledger_LedgerTypes` llt
						INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
						INNER JOIN `Platform_Regions` pr ON pr.`Id` = pd.`RegionId`  
						INNER JOIN `Platform_Regions` pr1 ON pr1.`Id` = pr.`ParentId` 
						INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id`
						INNER JOIN `Ledger_TableInfos` lti ON ll.`Id`=lti.`Id`
						WHERE pr.`Grade` = 4 and pr.`TreeCode` like '{cityRegionCode}%' AND ll.`IsOnline`=true AND ll.`Name` like '【%'
						AND ll.`IsDeleted`=false  AND llt.`IsDeleted`=false AND pd.`IsDeleted`=false AND pr.`IsDeleted`=false
		) t GROUP BY t.`RegionName`, t.`AuthDepartment`
) t1 GROUP BY `RegionName`;
";

        var connection = await GetDbConnectionAsync();
        return (await connection.QueryAsync<DistrictLedgerCountAndAuthDepartmentCountView>(sql)).ToList();
    }

    /// <summary>
    /// 获取区县台账表名及授权部门
    /// </summary>
    /// <returns></returns>
    public override async Task<List<DistrictTableNameAndAuthDepartmentView>>
        GetDistrictTableNameAndAuthDepartmentAsync(string cityRegionCode)
    {
        string sql = @$"
SELECT `RegionName`, `AuthDepartment`, `TableName` FROM (
SELECT pr.`Name` AS `RegionName`,
					llt.`Name` as `AuthDepartment`,
					lti.`Name` as `TableName`
		FROM `Ledger_LedgerTypes` llt
		INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
		INNER JOIN `Platform_Regions` pr ON pr.`Id` = pd.`RegionId`  
		INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id`
		INNER JOIN `Ledger_TableInfos` lti ON ll.`Id`=lti.`Id`
		WHERE pr.`Grade` = 3 and pr.`TreeCode` like '{cityRegionCode}%' AND ll.`IsOnline`=true AND ll.`Name` like '【%'
		AND ll.`IsDeleted`=false  AND llt.`IsDeleted`=false AND pd.`IsDeleted`=false AND pr.`IsDeleted`=false

UNION

SELECT pr1.`Name` AS `RegionName`,
					llt.`Name` as `AuthDepartment`,
					lti.`Name` as `TableName`
		FROM `Ledger_LedgerTypes` llt
		INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
		INNER JOIN `Platform_Regions` pr ON pr.`Id` = pd.`RegionId`  
		INNER JOIN `Platform_Regions` pr1 ON pr1.`Id` = pr.`ParentId` 
		INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id`
		INNER JOIN `Ledger_TableInfos` lti ON ll.`Id`=lti.`Id`
		WHERE pr.`Grade` = 4 and pr.`TreeCode` like '{cityRegionCode}%' AND ll.`IsOnline`=true AND ll.`Name` like '【%'
		AND ll.`IsDeleted`=false  AND llt.`IsDeleted`=false AND pd.`IsDeleted`=false AND pr.`IsDeleted`=false
		) t GROUP BY t.`RegionName`, t.`AuthDepartment`, t.`TableName`";

        var connection = await GetDbConnectionAsync();
        return (await connection.QueryAsync<DistrictTableNameAndAuthDepartmentView>(sql)).ToList();
    }

    /// <summary>
    /// 获取区县台账表名
    /// </summary>
    /// <returns></returns>
    public override async Task<Dictionary<string, List<string>>> GetDistrictTableNameAsync(string cityRegionCode)
    {
        string sql = @$"
SELECT `RegionName`, `TableName` FROM (
SELECT `RegionName`, `AuthDepartment`, `TableName` FROM (
SELECT pr.`Name` AS `RegionName`,
					llt.`Name` as `AuthDepartment`,
					lti.`Name` as `TableName`
		FROM `Ledger_LedgerTypes` llt
		INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
		INNER JOIN `Platform_Regions` pr ON pr.`Id` = pd.`RegionId`  
		INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id`
		INNER JOIN `Ledger_TableInfos` lti ON ll.`Id`=lti.`Id`
		WHERE pr.`Grade` = 3 and pr.`TreeCode` like '{cityRegionCode}%' AND ll.`IsOnline`=true AND ll.`Name` like '【%'
		AND ll.`IsDeleted`=false  AND llt.`IsDeleted`=false AND pd.`IsDeleted`=false AND pr.`IsDeleted`=false

UNION

SELECT pr1.`Name` AS `RegionName`,
					llt.`Name` as `AuthDepartment`,
					lti.`Name` as `TableName`
		FROM `Ledger_LedgerTypes` llt
		INNER JOIN `Platform_Departments` pd ON llt.`DepartmentId` = pd.`Id`
		INNER JOIN `Platform_Regions` pr ON pr.`Id` = pd.`RegionId`  
		INNER JOIN `Platform_Regions` pr1 ON pr1.`Id` = pr.`ParentId` 
		INNER JOIN `Ledger_Ledgers` ll ON ll.`LedgerTypeId` = llt.`Id`
		INNER JOIN `Ledger_TableInfos` lti ON ll.`Id`=lti.`Id`
		WHERE pr.`Grade` = 4 and pr.`TreeCode` like '{cityRegionCode}%' AND ll.`IsOnline`=true AND ll.`Name` like '【%'
		AND ll.`IsDeleted`=false  AND llt.`IsDeleted`=false AND pd.`IsDeleted`=false AND pr.`IsDeleted`=false
		) t GROUP BY t.`RegionName`, t.`AuthDepartment`, t.`TableName`
	) t1 GROUP BY `RegionName`, `TableName`;";

        var connection = await GetDbConnectionAsync();
        var result = (await connection.QueryAsync<RegionTableNameView>(sql)).ToList();
        return result
            .GroupBy(f => f.RegionName)
            .ToDictionary(f => f.Key,
                f => f.Select(s => s.TableName).ToList());
    }

    public override async Task<List<StreetTableNameAndAuthDistrictView>> GetStreetTableNameAndAuthDistrict(string cityRegionCode)
    {
        string sql = @$"
SELECT `RegionName`, `RegionGrade`, `District`, `Street`, `TableName` FROM (
	SELECT pr1.`Name` AS `RegionName`, pr1.`Grade` AS `RegionGrade`, pr2.`Name` AS `District`, pr.`Name` AS `Street`, lti.`Name` AS `TableName` FROM `Platform_Departments` pd
	INNER JOIN `Platform_Regions` pr on pd.`RegionId`=pr.`Id`
	INNER JOIN `Ledger_LedgerDepartments` lld on pd.`Id`=lld.`DepartmentId`
	INNER JOIN `Ledger_Ledgers` ll on lld.`LedgerId`=ll.`Id`
	INNER JOIN `Ledger_TableInfos` lti on lti.`Id`=ll.`Id`
	INNER JOIN `Ledger_LedgerUsers` llu on lld.`DepartmentId`=llu.`DepartmentId` and lld.`LedgerId`=llu.`LedgerId`
	INNER JOIN `Ledger_LedgerTypes` llt on ll.`LedgerTypeId`=llt.`Id`
	INNER JOIN `Platform_Departments` pd1 ON llt.`DepartmentId`=pd1.`Id`
	INNER JOIN `Platform_Regions` pr1 on pd1.`RegionId`=pr1.`Id`
	INNER JOIN `Platform_Regions` pr2 on pr.`ParentId`=pr2.`Id`
	WHERE pr.`Grade`=4 AND ll.`IsOnline`=true and pr1.`Grade`>=2 AND pr.`TreeCode` like '{cityRegionCode}%' AND ll.`Name` like '【%'
	AND ll.`IsDeleted`=false AND llt.`IsDeleted`=false AND pd.`IsDeleted`=false AND pr.`IsDeleted`=false AND pr1.`IsDeleted`=false AND pr2.`IsDeleted`=false
	GROUP BY pr1.`Name`, pr1.`Grade`, pr2.`Name`, pr.`Name`, lti.`Name`
	) T WHERE ((T.`RegionName`=T.`District` and T.`RegionGrade`=3) or (T.`RegionGrade`!=3))";


        var connection = await GetDbConnectionAsync();
        return (await connection.QueryAsync<StreetTableNameAndAuthDistrictView>(sql)).ToList();
    }

    public override async Task<List<StreetUserCountView>> GetStreetUserCount(string cityRegionCode)
    {
        string sql =
            @$"SELECT pr1.`Name` as `District`, pr.`Name` as `Street`, count(pud.`UserId`) as `UserCount` FROM `Platform_Departments` pd
INNER JOIN `Platform_Regions` pr on pd.`RegionId`=pr.`Id`
INNER JOIN `Platform_Regions` pr1 on pr.`ParentId`=pr1.`Id`
INNER JOIN `Platform_UserDepartments` pud on pd.`Id`=pud.`DepartmentId`
INNER JOIN `AbpUsers` au on pud.`UserId`=au.`Id`
WHERE pr.`Grade`=4 and pr.`TreeCode` like '{cityRegionCode}%'
and pr.`IsDeleted`=false and pd.`IsDeleted`=false and pr1.`IsDeleted`=false
GROUP BY pr1.`Name`, pr.`Name`";

        var connection = await GetDbConnectionAsync();
        return (await connection.QueryAsync<StreetUserCountView>(sql)).ToList();
    }

    /// <summary>
    /// 获取向上的授权链
    /// 向上的授权链包含自己,顺序为(假设A授权B,B授权C,则):C->B->A
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <param name="largeDepartmentId"></param>
    /// <returns></returns>
    public override async Task<List<Guid>> GetAuthorizationChainByUp(Guid ledgerId, Guid largeDepartmentId)
    {
        // 注意不要更改返回的Guid顺序
        string sql = @$"WITH RECURSIVE LedgerDepartmentHierarchy AS (
    SELECT 
        `DepartmentId`,
        `LedgerId`,
        `AuthDepartmentId`,
        CAST(`DepartmentId` AS CHAR(36)) AS `Visited`
    FROM
        `Ledger_LedgerDepartments`
    WHERE 
        `LedgerId` = @LedgerId
        AND `DepartmentId` = @DepartmentId
    
    UNION ALL
    
    SELECT
        lld.`DepartmentId`,
        lld.`LedgerId`,
        lld.`AuthDepartmentId`,
        CONCAT(dh.`Visited`, ',', lld.`DepartmentId`)
    FROM 
        LedgerDepartmentHierarchy dh
        INNER JOIN `Ledger_LedgerDepartments` lld 
            ON dh.`AuthDepartmentId` = lld.`DepartmentId`
            AND dh.`LedgerId` = lld.`LedgerId`
    WHERE 
        NOT FIND_IN_SET(lld.`DepartmentId`, dh.`Visited`)
)
SELECT lh.`DepartmentId`
FROM LedgerDepartmentHierarchy lh;";



        var parameters = new
        {
            LedgerId = ledgerId,
            DepartmentId = largeDepartmentId
        };

        // 不可更改返回顺序，如有需要自行在业务调整
        var connection = await GetDbConnectionAsync();
        return (await connection.QueryAsync<Guid>(sql, parameters)).ToList();
    }


    /// <summary>
    /// 获取向下的授权链
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <param name="largeDepartmentId"></param>
    /// <returns></returns>
    public override async Task<List<Guid>> GetAuthorizationChainByDown(Guid ledgerId, Guid largeDepartmentId)
    {
        string sql = @$"WITH RECURSIVE LedgerDepartmentHierarchy AS (
    SELECT 
        `DepartmentId`,
        `LedgerId`,
        `AuthDepartmentId`,
        CAST(`DepartmentId` AS CHAR(3699)) AS `Visited`,-- 36个字符针对单个UUId可以，这里使用CONCAT进行拼接可能会导致长度不够报错，递归层级限制为100层总字符为3699
        1 AS `Depth`
    FROM
        `Ledger_LedgerDepartments`
    WHERE 
        `LedgerId` = @LedgerId
        AND `AuthDepartmentId` = @DepartmentId
    
    UNION ALL
    
    SELECT
        lld.`DepartmentId`,
        lld.`LedgerId`,
        lld.`AuthDepartmentId`,
        CONCAT(dh.`Visited`, ',', lld.`DepartmentId`),
        dh.`Depth` + 1
    FROM 
        LedgerDepartmentHierarchy dh
        INNER JOIN `Ledger_LedgerDepartments` lld 
            ON dh.`DepartmentId` = lld.`AuthDepartmentId`
            AND dh.`LedgerId` = lld.`LedgerId`
    WHERE 
        NOT FIND_IN_SET(lld.`DepartmentId`, dh.`Visited`)
        AND dh.`Depth` < 100 -- 设置递归深度限制，例如100层
)
SELECT DISTINCT `DepartmentId`
FROM LedgerDepartmentHierarchy;
";

        var parameters = new
        {
            LedgerId = ledgerId,
            DepartmentId = largeDepartmentId
        };

        // 不可更改返回顺序，如有需要自行在业务调整
        var connection = await GetDbConnectionAsync();
        var guids = (await connection.QueryAsync<Guid>(sql, parameters)).ToList();

        if (!guids.Contains(largeDepartmentId))
            guids.AddLast(largeDepartmentId);

        return guids;
    }

    /// <summary>
    /// 获取向下的授权链(仅具有填报权限)
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <param name="largeDepartmentId"></param>
    /// <returns></returns>
    public override async Task<List<Guid>> GetFillingAuthorizationChainByDown(Guid ledgerId, Guid largeDepartmentId)
    {
        string sql = @$"WITH RECURSIVE LedgerDepartmentHierarchy AS (
    SELECT 
        `DepartmentId`,
        `LedgerId`,
        `AuthDepartmentId`,
        CAST(`DepartmentId` AS CHAR(36)) AS `Visited`,
        1 AS `Depth`
    FROM
        `Ledger_LedgerDepartments`
    WHERE 
        `LedgerId` = @LedgerId
        AND `AuthDepartmentId` = @DepartmentId
        AND (`PermissionFlags` & 14) != 0  -- 检查填报权限 (Create|Update|Delete = 2|4|8 = 14)
    
    UNION ALL
    
    SELECT
        lld.`DepartmentId`,
        lld.`LedgerId`,
        lld.`AuthDepartmentId`,
        CONCAT(dh.`Visited`, ',', lld.`DepartmentId`),
        dh.`Depth` + 1
    FROM 
        LedgerDepartmentHierarchy dh
        INNER JOIN `Ledger_LedgerDepartments` lld 
            ON dh.`DepartmentId` = lld.`AuthDepartmentId`
            AND dh.`LedgerId` = lld.`LedgerId`
    WHERE 
        NOT FIND_IN_SET(lld.`DepartmentId`, dh.`Visited`)
        AND (lld.`PermissionFlags` & 14) != 0  -- 检查填报权限 (Create|Update|Delete = 2|4|8 = 14)
        AND dh.`Depth` < 1000 -- 设置递归深度限制，例如100层
)
SELECT DISTINCT `DepartmentId`
FROM LedgerDepartmentHierarchy;
";

        var parameters = new
        {
            LedgerId = ledgerId,
            DepartmentId = largeDepartmentId
        };

        // 不可更改返回顺序，如有需要自行在业务调整
        var connection = await GetDbConnectionAsync();
        var guids = (await connection.QueryAsync<Guid>(sql, parameters)).ToList();

        if (!guids.Contains(largeDepartmentId))
            guids.AddLast(largeDepartmentId);

        return guids;
    }

    /// <summary>
    /// 根据区域获取部门的授权信息
    /// </summary>
    /// <param name="regionId"></param>
    /// <param name="largeDepartmentId"></param>
    /// <returns></returns>
    public override async Task<List<DepartmentSmallView>> GetAuthDepartmentByRegionAsync(Guid regionId, Guid largeDepartmentId)
    {
        string sql = @"
SELECT pd.`Id`, pd.`Name` FROM `Platform_Regions` pr inner join `Platform_Departments` pd on pr.`Id`=pd.`RegionId`
inner join `Ledger_LedgerDepartments` lld on pd.`Id`=lld.`DepartmentId`
where pr.`Id`=@RegionId and lld.`AuthDepartmentId`=@LargeDepartmentId
group by pd.`Id`, pd.`Name` 
order by pr.`Name` ASC";

        var parameters = new
        {
            RegionId = regionId,
            LargeDepartmentId = largeDepartmentId
        };

        var connection = await GetDbConnectionAsync();
        return (await connection.QueryAsync<DepartmentSmallView>(sql, parameters)).ToList();
    }

    /// <summary>
    /// 根据区域获取子级部门的授权信息
    /// </summary>
    /// <param name="regionId"></param>
    /// <param name="largeDepartmentId"></param>
    /// <returns></returns>
    public override async Task<List<DepartmentSmallView>> GetChildAuthDepartmentByRegionAsync(Guid regionId, Guid largeDepartmentId)
    {
        string sql = @"
SELECT pd.""Id"",pd.""Name"" FROM ""Platform_Regions"" pr inner join ""Platform_Departments"" pd on pr.""Id""=pd.""RegionId""
inner join ""Ledger_LedgerDepartments"" lld on pd.""Id""=lld.""DepartmentId""
where pr.""ParentId""=@ParentRegionId and lld.""AuthDepartmentId""=@LargeDepartmentId
group by pd.""Id"",pd.""Name"" 
order by pr.""Name"" ASC";

        var parameters = new
        {
            ParentRegionId = regionId,
            LargeDepartmentId = largeDepartmentId
        };

        var connection = await GetDbConnectionAsync();
        return (await connection.QueryAsync<DepartmentSmallView>(sql, parameters)).ToList();
    }
    
    
    public override async Task<List<Guid>> GetDepartmentExtendIdsByRegionCodeAsync(string regionCode)
    {
        string sql =
            @"SELECT
	d.""Id"" 
FROM
	""Platform_Departments"" AS d 
WHERE
	d.""RegionId"" IN ( SELECT r.""Id"" FROM ""Platform_Regions"" AS r WHERE r.""TreeCode"" LIKE @RegionCodePattern AND r.""IsDeleted"" != TRUE) 
	AND d.""Id"" IN (
	SELECT
		d.""DepartmentExtendId"" 
	FROM
		""Platform_Departments"" AS d 
	WHERE
		d.""RegionId"" IN ( SELECT r.""Id"" FROM ""Platform_Regions"" AS r WHERE r.""TreeCode"" LIKE @RegionCodePattern AND r.""IsDeleted"" != TRUE) 
	) 
	AND d.""IsDeleted"" != TRUE;
	";

        var connection = await GetDbConnectionAsync();
    
        return (await connection.QueryAsync<Guid>(sql, new { RegionCodePattern = $"{regionCode}%" })).ToList();
    }
    
    /// <summary>
    /// 检查账本和账本用户是否同时存在于指定区域
    /// </summary>
    /// <param name="treeCode">区域树形编码</param>
    /// <param name="ledgerId">账本ID</param>
    /// <returns>当账本和账本用户都存在时返回true，否则返回false</returns>
    public override async Task<bool> ExistsLedgerUserByRegionAndLedgerAsync(string treeCode, Guid ledgerId)
    {
        var connection = await GetDbConnectionAsync();
    
        // 查询业务表授权给了区域下的用户则区县运维员也可查看
        string ledgerUserSql = @"
        SELECT EXISTS (
            SELECT 1 
            FROM ""Ledger_LedgerUsers"" llu 
            INNER JOIN ""Platform_Departments"" pd ON llu.""DepartmentId"" = pd.""DepartmentExtendId"" 
            INNER JOIN ""Platform_Regions"" pr ON pr.""Id"" = pd.""RegionId"" 
            WHERE pr.""TreeCode"" LIKE @TreeCodePattern 
                AND llu.""LedgerId"" = @LedgerId
                AND pd.""IsDeleted"" != TRUE
                AND pr.""IsDeleted"" != TRUE
        )";
    
        // 查询业务表创建部门在该区域下则区县运维员也可查看
        string ledgerSql = @"
        SELECT EXISTS (
            SELECT 1
            FROM ""Ledger_Ledgers"" ll
            INNER JOIN ""Platform_Departments"" pd ON ll.""CreatorLargeDepartmentId"" = pd.""DepartmentExtendId"" 
            INNER JOIN ""Platform_Regions"" pr ON pr.""Id"" = pd.""RegionId"" 
            WHERE pr.""TreeCode"" LIKE @TreeCodePattern 
                AND ll.""Id"" = @LedgerId
                AND pd.""IsDeleted"" != TRUE
                AND pr.""IsDeleted"" != TRUE
        )";

        var parameters = new { TreeCodePattern = treeCode + "%", LedgerId = ledgerId };
    
        // 执行两个查询
        bool ledgerUserExists = await connection.ExecuteScalarAsync<bool>(ledgerUserSql, parameters);
        bool ledgerExists = await connection.ExecuteScalarAsync<bool>(ledgerSql, parameters);

        // 都不存在则该台账不在该区域下
        return ledgerUserExists || ledgerExists;
    }
}